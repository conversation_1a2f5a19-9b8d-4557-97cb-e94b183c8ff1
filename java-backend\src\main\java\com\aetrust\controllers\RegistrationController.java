package com.aetrust.controllers;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.dto.ApiResponse;
import com.aetrust.services.RegistrationService;
import com.aetrust.services.SecurityService;
import com.aetrust.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/registration")
@Validated
public class RegistrationController {
    
    @Autowired
    private RegistrationService registrationService;
    
    @Autowired
    private SecurityService securityService;
    
    @PostMapping("/initiate")
    public ResponseEntity<ApiResponse<Map<String, Object>>> initiateRegistration(
            @Valid @RequestBody RegistrationInitRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/register/initiate");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            SecurityService.ActivityContext activity = new SecurityService.ActivityContext(
                ipAddress, userAgent, System.currentTimeMillis());
            
            SecurityService.SuspiciousActivityResult suspiciousResult = 
                securityService.detectSuspiciousActivity("anonymous", activity);
            
            if (suspiciousResult.getRiskScore() > 50) {
                return ResponseEntity.status(403).body(
                    ApiResponse.error("registration blocked due to suspicious activity", "SUSPICIOUS_ACTIVITY"));
            }
            
            // initiate
            RegistrationService.RegistrationResult result = registrationService.initiateRegistration(request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("registrationId", result.getRegistrationId());
            responseData.put("nextStep", result.getNextStep());
            responseData.put("phoneVerificationRequired", true);
            responseData.put("expiresIn", 900); // 15 minutes
            
            return ResponseEntity.ok(
                ApiResponse.success("registration initiated successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error initiating registration: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("registration initiation failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/verify-phone")
    public ResponseEntity<ApiResponse<Map<String, Object>>> verifyPhone(
            @Valid @RequestBody PhoneVerificationRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = getClientIp(httpRequest);
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getPhone(), "user", "/auth/register/verify-phone");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many verification attempts", "RATE_LIMIT_EXCEEDED"));
            }
            
            RegistrationService.VerificationResult result = registrationService.verifyPhone(request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("phoneVerified", true);
            responseData.put("nextStep", result.getNextStep());
            responseData.put("registrationId", result.getRegistrationId());
            
            if (result.isCompleted()) {
                responseData.put("accessToken", result.getAccessToken());
                responseData.put("refreshToken", result.getRefreshToken());
                responseData.put("userProfile", result.getUserProfile());
            }
            
            return ResponseEntity.ok(
                ApiResponse.success("phone verification successful", responseData));
                
        } catch (Exception error) {
            log.error("Error verifying phone: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("phone verification failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/verify-email")
    public ResponseEntity<ApiResponse<Map<String, Object>>> verifyEmail(
            @Valid @RequestBody EmailVerificationRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = getClientIp(httpRequest);
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getEmail(), "user", "/auth/register/verify-email");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many verification attempts", "RATE_LIMIT_EXCEEDED"));
            }
            
            RegistrationService.VerificationResult result = registrationService.verifyEmail(request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("emailVerified", true);
            responseData.put("nextStep", result.getNextStep());
            responseData.put("registrationId", result.getRegistrationId());
            
            return ResponseEntity.ok(
                ApiResponse.success("email verification successful", responseData));
                
        } catch (Exception error) {
            log.error("Error verifying email: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("email verification failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/complete")
    public ResponseEntity<ApiResponse<Map<String, Object>>> completeRegistration(
            @Valid @RequestBody RegistrationCompleteRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            RegistrationService.CompletionResult result = registrationService.completeRegistration(request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("registrationCompleted", true);
            responseData.put("accessToken", result.getAccessToken());
            responseData.put("refreshToken", result.getRefreshToken());
            responseData.put("userProfile", result.getUserProfile());
            responseData.put("walletCreated", result.isWalletCreated());
            
            log.info("Registration completed successfully for user: {}", 
                securityService.maskSensitiveData(result.getUserProfile().get("email").toString()));
            
            return ResponseEntity.ok(
                ApiResponse.success("registration completed successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error completing registration: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("registration completion failed", "INTERNAL_ERROR"));
        }
    }
    
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRegistrationStatus(
            @RequestParam String phone,
            HttpServletRequest httpRequest) {
        
        try {
            RegistrationService.StatusResult result = registrationService.getRegistrationStatus(phone);
            
            if (!result.isFound()) {
                return ResponseEntity.notFound().build();
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("registrationExists", true);
            responseData.put("currentStep", result.getCurrentStep());
            responseData.put("phoneVerified", result.isPhoneVerified());
            responseData.put("emailVerified", result.isEmailVerified());
            responseData.put("isCompleted", result.isCompleted());
            responseData.put("expiresAt", result.getExpiresAt());
            
            return ResponseEntity.ok(
                ApiResponse.success("registration status retrieved", responseData));
                
        } catch (Exception error) {
            log.error("Error getting registration status: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to get registration status", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/personal-info")
    public ResponseEntity<ApiResponse<Map<String, Object>>> completePersonalInfo(
            @Valid @RequestBody CompletePersonalInfoRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            String ipAddress = getClientIp(httpRequest);

            RegistrationService.PersonalInfoResult result = registrationService.completePersonalInfo(
                userPayload.getId(), request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("personalInfoCompleted", true);
            responseData.put("nextStep", result.getNextStep());
            responseData.put("profileCompleteness", result.getProfileCompleteness());

            return ResponseEntity.ok(
                ApiResponse.success("personal information completed successfully", responseData));

        } catch (Exception error) {
            log.error("Error completing personal info: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to complete personal info", "INTERNAL_ERROR"));
        }
    }

    @PostMapping("/identity-verification")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitIdentityVerification(
            @Valid @RequestBody SubmitIdentityRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            RegistrationService.IdentityVerificationResult result = registrationService.submitIdentityVerification(
                userPayload.getId(), request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("identitySubmitted", true);
            responseData.put("verificationStatus", result.getVerificationStatus());
            responseData.put("nextStep", result.getNextStep());
            responseData.put("estimatedReviewTime", "24-48 hours");

            return ResponseEntity.ok(
                ApiResponse.success("identity verification submitted successfully", responseData));

        } catch (Exception error) {
            log.error("Error submitting identity verification: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to submit identity verification", "INTERNAL_ERROR"));
        }
    }

    @PostMapping("/transaction-pin")
    public ResponseEntity<ApiResponse<Map<String, Object>>> setTransactionPin(
            @Valid @RequestBody SetTransactionPinRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            RegistrationService.TransactionPinResult result = registrationService.setTransactionPin(
                userPayload.getId(), request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("transactionPinSet", true);
            responseData.put("securityLevel", result.getSecurityLevel());
            responseData.put("nextStep", result.getNextStep());

            return ResponseEntity.ok(
                ApiResponse.success("transaction pin set successfully", responseData));

        } catch (Exception error) {
            log.error("Error setting transaction pin: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to set transaction pin", "INTERNAL_ERROR"));
        }
    }

    @PostMapping("/biometric-enrollment")
    public ResponseEntity<ApiResponse<Map<String, Object>>> setBiometricEnrollment(
            @Valid @RequestBody BiometricEnrollmentRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            RegistrationService.BiometricResult result = registrationService.setBiometricEnrollment(
                userPayload.getId(), request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("biometricEnrolled", true);
            responseData.put("enrollmentType", result.getEnrollmentType());
            responseData.put("securityLevel", result.getSecurityLevel());
            responseData.put("nextStep", result.getNextStep());

            return ResponseEntity.ok(
                ApiResponse.success("biometric enrollment completed successfully", responseData));

        } catch (Exception error) {
            log.error("Error setting biometric enrollment: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to set biometric enrollment", "INTERNAL_ERROR"));
        }
    }

    @PostMapping("/business-verification")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitBusinessVerification(
            @Valid @RequestBody BusinessVerificationRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            RegistrationService.BusinessVerificationResult result = registrationService.submitBusinessVerification(
                userPayload.getId(), request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("businessVerificationSubmitted", true);
            responseData.put("verificationStatus", result.getVerificationStatus());
            responseData.put("businessType", result.getBusinessType());
            responseData.put("estimatedReviewTime", "3-5 business days");

            return ResponseEntity.ok(
                ApiResponse.success("business verification submitted successfully", responseData));

        } catch (Exception error) {
            log.error("Error submitting business verification: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to submit business verification", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/progress")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRegistrationProgress(
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            RegistrationService.ProgressResult result = registrationService.getRegistrationProgress(
                userPayload.getId());

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("currentStep", result.getCurrentStep());
            responseData.put("completedSteps", result.getCompletedSteps());
            responseData.put("totalSteps", result.getTotalSteps());
            responseData.put("progressPercentage", result.getProgressPercentage());
            responseData.put("nextSteps", result.getNextSteps());
            responseData.put("isCompleted", result.isCompleted());

            return ResponseEntity.ok(
                ApiResponse.success("registration progress retrieved successfully", responseData));

        } catch (Exception error) {
            log.error("Error getting registration progress: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to get registration progress", "INTERNAL_ERROR"));
        }
    }

    @PostMapping("/resend-verification")
    public ResponseEntity<ApiResponse<Map<String, Object>>> resendVerification(
            @Valid @RequestBody ResendVerificationRequest request,
            HttpServletRequest httpRequest) {

        try {
            String ipAddress = getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getIdentifier(), "user", "/auth/register/resend-verification");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many resend attempts", "RATE_LIMIT_EXCEEDED"));
            }

            RegistrationService.ResendResult result = registrationService.resendVerification(request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("verificationSent", true);
            responseData.put("verificationType", result.getVerificationType());
            responseData.put("expiresIn", 300); // 5 minutes

            return ResponseEntity.ok(
                ApiResponse.success("verification code sent successfully", responseData));

        } catch (Exception error) {
            log.error("Error resending verification: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to resend verification", "INTERNAL_ERROR"));
        }
    }
    
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
