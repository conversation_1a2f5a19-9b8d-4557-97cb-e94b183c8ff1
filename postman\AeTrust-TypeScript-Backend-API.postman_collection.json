{"info": {"name": "AeTrust TypeScript Backend API", "description": "API collection for AeTrust TypeScript backend", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1", "type": "string", "description": "TypeScript Backend Base URL"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}], "item": [{"name": "TypeScript Authentication", "item": [{"name": "Register - Initiate", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+250788123456\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"password\": \"SecurePass123!\",\n  \"userType\": \"individual\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/initiate", "host": ["{{baseUrl}}"], "path": ["auth", "register", "initiate"]}}}, {"name": "Register - Verify Phone", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.accessToken) {", "        pm.collectionVariables.set('authToken', response.data.accessToken);", "        console.log('Auth token saved:', response.data.accessToken);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+250788123456\",\n  \"code\": \"123456\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/verify-phone", "host": ["{{baseUrl}}"], "path": ["auth", "register", "verify-phone"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.token) {", "        pm.collectionVariables.set('authToken', response.data.token);", "        pm.collectionVariables.set('userId', response.data.user.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}]}, {"name": "TypeScript User Management", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"bio\": \"Updated bio\",\n  \"address\": {\n    \"street\": \"456 New St\",\n    \"city\": \"Kigali\",\n    \"state\": \"Kigali\",\n    \"country\": \"Rwanda\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/users/profile", "host": ["{{baseUrl}}"], "path": ["users", "profile"]}}}, {"name": "Get User Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard", "host": ["{{baseUrl}}"], "path": ["users", "dashboard"]}}}, {"name": "Get Enhanced Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard/enhanced", "host": ["{{baseUrl}}"], "path": ["users", "dashboard", "enhanced"]}}}, {"name": "Get Wallet Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard/wallet", "host": ["{{baseUrl}}"], "path": ["users", "dashboard", "wallet"]}}}, {"name": "Get User Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/stats", "host": ["{{baseUrl}}"], "path": ["users", "stats"]}}}, {"name": "Search Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/search?search=john&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["users", "search"], "query": [{"key": "search", "value": "john"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}]}]}