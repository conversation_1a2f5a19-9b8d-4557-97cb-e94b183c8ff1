{"info": {"_postman_id": "aetrust-java-backend-api", "name": "AeTrust Java Backend API", "description": "Complete API collection for AeTrust Java Backend with authentication, registration, and user management endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "aetrust-team"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"access_token\": \"{{access_token}}\",\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}, "response": []}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/auth/forgot-password", "host": ["{{base_url}}"], "path": ["auth", "forgot-password"]}}, "response": []}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset-token-here\",\n  \"new_password\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/auth/reset-password", "host": ["{{base_url}}"], "path": ["auth", "reset-password"]}}, "response": []}]}, {"name": "Registration", "item": [{"name": "Initiate Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+1234567890\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"password\": \"Password123!\",\n  \"userType\": \"individual\",\n  \"platform\": \"web\",\n  \"dateOfBirth\": \"1990-01-01\"\n}"}, "url": {"raw": "{{base_url}}/auth/register/initiate", "host": ["{{base_url}}"], "path": ["auth", "register", "initiate"]}}, "response": []}, {"name": "Verify Phone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+1234567890\",\n  \"code\": \"123456\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{base_url}}/auth/register/verify-phone", "host": ["{{base_url}}"], "path": ["auth", "register", "verify-phone"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"code\": \"123456\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{base_url}}/auth/register/verify-email", "host": ["{{base_url}}"], "path": ["auth", "register", "verify-email"]}}, "response": []}, {"name": "Complete Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+1234567890\",\n  \"pin\": \"1234\",\n  \"confirmPin\": \"1234\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{base_url}}/auth/register/complete", "host": ["{{base_url}}"], "path": ["auth", "register", "complete"]}}, "response": []}, {"name": "Resend Verification Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+1234567890\",\n  \"codeType\": \"sms\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{base_url}}/auth/register/resend-code", "host": ["{{base_url}}"], "path": ["auth", "register", "resend-code"]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/users/me", "host": ["{{base_url}}"], "path": ["users", "me"]}}, "response": []}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"username\": \"johndo<PERSON>\",\n  \"bio\": \"Software developer\",\n  \"address\": {\n    \"street\": \"123 Main St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"country\": \"USA\",\n    \"postalCode\": \"10001\"\n  }\n}"}, "url": {"raw": "{{base_url}}/users/profile", "host": ["{{base_url}}"], "path": ["users", "profile"]}}, "response": []}, {"name": "Update Password", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"OldPassword123!\",\n  \"newPassword\": \"NewPassword123!\",\n  \"confirmPassword\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/users/password", "host": ["{{base_url}}"], "path": ["users", "password"]}}, "response": []}, {"name": "Upload Profile Picture", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"fileData\": \"base64-encoded-image-data\",\n  \"fileName\": \"profile.jpg\",\n  \"fileType\": \"image/jpeg\",\n  \"fileSize\": 1024000\n}"}, "url": {"raw": "{{base_url}}/users/profile-picture", "host": ["{{base_url}}"], "path": ["users", "profile-picture"]}}, "response": []}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"Password123!\",\n  \"confirmation\": \"DELETE\",\n  \"reason\": \"No longer needed\"\n}"}, "url": {"raw": "{{base_url}}/users/account", "host": ["{{base_url}}"], "path": ["users", "account"]}}, "response": []}, {"name": "Search Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/users/search?query=john&limit=10", "host": ["{{base_url}}"], "path": ["users", "search"], "query": [{"key": "query", "value": "john"}, {"key": "limit", "value": "10"}]}}, "response": []}]}, {"name": "Health & Monitoring", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "Auth Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/health", "host": ["{{base_url}}"], "path": ["auth", "health"]}}, "response": []}, {"name": "User Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/health", "host": ["{{base_url}}"], "path": ["users", "health"]}}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}]}