#!/usr/bin/env python3
"""
Script to replace redisTemplate calls with cacheService calls in SecurityService.java
"""

import re

def fix_redis_template_calls(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Common replacements
    replacements = [
        # Basic get operations
        (r'redisTemplate\.opsForValue\(\)\.get\(([^)]+)\)', r'cacheService.get(\1)'),
        
        # Basic set operations with TTL
        (r'redisTemplate\.opsForValue\(\)\.set\(([^,]+),\s*([^,]+),\s*([^,]+),\s*TimeUnit\.([A-Z]+)\)', 
         r'cacheService.set(\1, \2, \3, TimeUnit.\4)'),
        
        # Basic set operations without TTL
        (r'redisTemplate\.opsForValue\(\)\.set\(([^,]+),\s*([^)]+)\)', 
         r'cacheService.set(\1, \2)'),
        
        # Delete operations
        (r'redisTemplate\.delete\(([^)]+)\)', r'cacheService.delete(\1)'),
        
        # Increment operations
        (r'redisTemplate\.opsForValue\(\)\.increment\(([^)]+)\)', r'cacheService.increment(\1)'),
        
        # Expire operations
        (r'redisTemplate\.expire\(([^,]+),\s*([^,]+),\s*TimeUnit\.([A-Z]+)\)', 
         r'cacheService.expire(\1, \2, TimeUnit.\3)'),
        
        # Set operations (for collections) - simplified to string storage
        (r'redisTemplate\.opsForSet\(\)\.members\(([^)]+)\)', 
         r'parseStringSet(cacheService.get(\1))'),
        
        (r'redisTemplate\.opsForSet\(\)\.add\(([^,]+),\s*([^)]+)\)', 
         r'cacheService.set(\1, serializeStringSet(\2))'),
        
        # GetExpire operations - simplified since cache service doesn't expose TTL
        (r'redisTemplate\.getExpire\(([^,]+),\s*TimeUnit\.[A-Z]+\)', 
         r'(cacheService.exists(\1) ? 300L : 0L)'),
        
        # Complex operations that need manual handling
        (r'redisTemplate\.opsForValue\(\)\.getOperations\(\)\.getExpire\([^)]+\)', 
         r'null'),
    ]
    
    # Apply replacements
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Add helper methods at the end of the class (before the last closing brace)
    helper_methods = '''
    
    // Helper methods for Set operations with cache service
    private Set<String> parseStringSet(String data) {
        if (data == null || data.trim().isEmpty()) {
            return new HashSet<>();
        }
        try {
            return new HashSet<>(Arrays.asList(data.split(",")));
        } catch (Exception e) {
            return new HashSet<>();
        }
    }
    
    private String serializeStringSet(Set<String> set) {
        if (set == null || set.isEmpty()) {
            return "";
        }
        return String.join(",", set);
    }
    
    private String serializeStringSet(String... items) {
        if (items == null || items.length == 0) {
            return "";
        }
        return String.join(",", items);
    }
'''
    
    # Find the last closing brace and insert helper methods before it
    last_brace_pos = content.rfind('}')
    if last_brace_pos != -1:
        content = content[:last_brace_pos] + helper_methods + '\n' + content[last_brace_pos:]
    
    # Add necessary imports
    imports_to_add = [
        'import java.util.Set;',
        'import java.util.HashSet;',
        'import java.util.Arrays;'
    ]
    
    for import_stmt in imports_to_add:
        if import_stmt not in content:
            # Find the last import statement
            import_pattern = r'(import [^;]+;)'
            matches = list(re.finditer(import_pattern, content))
            if matches:
                last_import_pos = matches[-1].end()
                content = content[:last_import_pos] + '\n' + import_stmt + content[last_import_pos:]
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed redisTemplate calls in {file_path}")

if __name__ == "__main__":
    fix_redis_template_calls("src/main/java/com/aetrust/services/SecurityService.java")
