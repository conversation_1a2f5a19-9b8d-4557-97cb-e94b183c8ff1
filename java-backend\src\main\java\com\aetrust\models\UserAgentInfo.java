package com.aetrust.models;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_agent_info", indexes = {
    @Index(name = "idx_agent_info_user_id", columnList = "userId"),
    @Index(name = "idx_agent_info_uuid", columnList = "userUuid"),
    @Index(name = "idx_agent_info_active", columnList = "isActive"),
    @Index(name = "idx_agent_info_verification", columnList = "verificationStatus")
})
@EntityListeners(AuditingEntityListener.class)
public class UserAgentInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false, unique = true)
    private Long userId;

    @Column(name = "user_uuid", nullable = false, unique = true)
    private UUID userUuid;

    @Column(name = "commission_rate", precision = 5, scale = 2)
    private BigDecimal commissionRate = BigDecimal.ZERO;

    @Column(name = "total_transactions")
    private Integer totalTransactions = 0;

    @Column(name = "total_commission_earned", precision = 15, scale = 2)
    private BigDecimal totalCommissionEarned = BigDecimal.ZERO;

    @Column(name = "is_active")
    private boolean isActive = true;

    @Column(name = "business_name")
    private String businessName;

    @Column(name = "business_type", length = 100)
    private String businessType;

    @Column(name = "business_registration_number", length = 100)
    private String businessRegistrationNumber;

    @Column(name = "business_address", columnDefinition = "TEXT")
    private String businessAddress;

    @Column(name = "business_document", length = 500)
    private String businessDocument;

    @Column(name = "tax_certificate", length = 500)
    private String taxCertificate;

    @Column(name = "verification_status", length = 20)
    private String verificationStatus = "PENDING";

    @Column(name = "monthly_commission", precision = 15, scale = 2)
    private BigDecimal monthlyCommission = BigDecimal.ZERO;

    @Column(name = "last_commission_date")
    private LocalDateTime lastCommissionDate;

    @Column(name = "performance_rating", precision = 3, scale = 2)
    private BigDecimal performanceRating = BigDecimal.ZERO;

    @Column(name = "successful_transactions")
    private Integer successfulTransactions = 0;

    @CreatedDate
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    // helper methods
    public boolean isVerified() {
        return "VERIFIED".equals(verificationStatus);
    }

    public boolean isPending() {
        return "PENDING".equals(verificationStatus);
    }

    public boolean isRejected() {
        return "REJECTED".equals(verificationStatus);
    }

    public BigDecimal getSuccessRate() {
        if (totalTransactions == null || totalTransactions == 0) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(successfulTransactions)
                .divide(new BigDecimal(totalTransactions), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(100));
    }

    public void addTransaction(BigDecimal commissionAmount, boolean successful) {
        totalTransactions = (totalTransactions == null) ? 1 : totalTransactions + 1;
        
        if (successful) {
            successfulTransactions = (successfulTransactions == null) ? 1 : successfulTransactions + 1;
            
            if (commissionAmount != null && commissionAmount.compareTo(BigDecimal.ZERO) > 0) {
                totalCommissionEarned = totalCommissionEarned.add(commissionAmount);
                monthlyCommission = monthlyCommission.add(commissionAmount);
                lastCommissionDate = LocalDateTime.now();
            }
        }
        
        // update performance rating based on success rate
        updatePerformanceRating();
    }

    private void updatePerformanceRating() {
        BigDecimal successRate = getSuccessRate();
        if (successRate.compareTo(new BigDecimal(95)) >= 0) {
            performanceRating = new BigDecimal("5.00");
        } else if (successRate.compareTo(new BigDecimal(90)) >= 0) {
            performanceRating = new BigDecimal("4.50");
        } else if (successRate.compareTo(new BigDecimal(85)) >= 0) {
            performanceRating = new BigDecimal("4.00");
        } else if (successRate.compareTo(new BigDecimal(80)) >= 0) {
            performanceRating = new BigDecimal("3.50");
        } else if (successRate.compareTo(new BigDecimal(75)) >= 0) {
            performanceRating = new BigDecimal("3.00");
        } else {
            performanceRating = new BigDecimal("2.50");
        }
    }

    public void resetMonthlyCommission() {
        monthlyCommission = BigDecimal.ZERO;
    }

    public boolean hasRequiredDocuments() {
        return businessName != null && !businessName.trim().isEmpty() &&
               businessType != null && !businessType.trim().isEmpty() &&
               businessRegistrationNumber != null && !businessRegistrationNumber.trim().isEmpty() &&
               businessDocument != null && !businessDocument.trim().isEmpty();
    }
}
