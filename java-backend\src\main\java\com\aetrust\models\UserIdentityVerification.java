package com.aetrust.models;

import com.aetrust.types.Types.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_identity_verification", indexes = {
    @Index(name = "idx_identity_verification_user_id", columnList = "userId"),
    @Index(name = "idx_identity_verification_uuid", columnList = "userUuid"),
    @Index(name = "idx_identity_verification_status", columnList = "verificationStatus")
})
@EntityListeners(AuditingEntityListener.class)
public class UserIdentityVerification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false, unique = true)
    private Long userId;

    @Column(name = "user_uuid", nullable = false, unique = true)
    private UUID userUuid;

    @Enumerated(EnumType.STRING)
    @Column(name = "id_type", length = 20)
    private IdType idType;

    @Column(name = "id_number", length = 100)
    private String idNumber;

    @Column(name = "id_document_front", length = 500)
    private String idDocumentFront;

    @Column(name = "id_document_back", length = 500)
    private String idDocumentBack;

    @Column(name = "selfie_photo", length = 500)
    private String selfiePhoto;

    @Enumerated(EnumType.STRING)
    @Column(name = "verification_status", length = 20)
    private VerificationStatus verificationStatus = VerificationStatus.PENDING;

    @Column(name = "verified_at")
    private LocalDateTime verifiedAt;

    @Column(name = "rejection_reason", columnDefinition = "TEXT")
    private String rejectionReason;

    @Column(name = "verification_notes", columnDefinition = "TEXT")
    private String verificationNotes;

    @Column(name = "verifier_id")
    private Long verifierId;

    @CreatedDate
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    // helper methods
    public boolean isVerified() {
        return verificationStatus == VerificationStatus.VERIFIED;
    }

    public boolean isPending() {
        return verificationStatus == VerificationStatus.PENDING;
    }

    public boolean isRejected() {
        return verificationStatus == VerificationStatus.REJECTED;
    }

    public boolean hasRequiredDocuments() {
        return idType != null && 
               idNumber != null && !idNumber.trim().isEmpty() &&
               idDocumentFront != null && !idDocumentFront.trim().isEmpty() &&
               selfiePhoto != null && !selfiePhoto.trim().isEmpty();
    }

    public boolean requiresBackDocument() {
        return idType == IdType.NATIONAL_ID || 
               idType == IdType.DRIVERS_LICENSE;
    }

    public boolean hasCompleteDocuments() {
        boolean hasRequired = hasRequiredDocuments();
        if (requiresBackDocument()) {
            return hasRequired && 
                   idDocumentBack != null && 
                   !idDocumentBack.trim().isEmpty();
        }
        return hasRequired;
    }

    public void approve(Long verifierId, String notes) {
        this.verificationStatus = VerificationStatus.VERIFIED;
        this.verifiedAt = LocalDateTime.now();
        this.verifierId = verifierId;
        this.verificationNotes = notes;
        this.rejectionReason = null;
    }

    public void reject(Long verifierId, String reason, String notes) {
        this.verificationStatus = VerificationStatus.REJECTED;
        this.verifierId = verifierId;
        this.rejectionReason = reason;
        this.verificationNotes = notes;
        this.verifiedAt = null;
    }

    public void reset() {
        this.verificationStatus = VerificationStatus.PENDING;
        this.verifiedAt = null;
        this.verifierId = null;
        this.rejectionReason = null;
        this.verificationNotes = null;
    }
}
