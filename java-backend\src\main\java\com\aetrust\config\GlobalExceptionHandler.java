package com.aetrust.config;

import com.aetrust.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.validation.FieldError;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Global Exception Handler for Route Not Found and Other Errors
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    
    // ANSI Color codes for console logging
    private static final String RESET = "\u001B[0m";
    private static final String RED = "\u001B[31m";
    private static final String YELLOW = "\u001B[33m";
    private static final String CYAN = "\u001B[36m";
    private static final String BOLD = "\u001B[1m";
    
    /**
     * Handle route not found (404 errors)
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleNoHandlerFound(
            NoHandlerFoundException ex, HttpServletRequest request) {
        
        String method = ex.getHttpMethod();
        String url = ex.getRequestURL();
        
        // Colored console output
        System.out.println();
        System.out.println(RED + BOLD + "🚫 ROUTE NOT FOUND" + RESET);
        System.out.println(CYAN + "📍 Method: " + BOLD + method + RESET);
        System.out.println(CYAN + "🔗 URL: " + BOLD + url + RESET);
        System.out.println(CYAN + "🌐 Client IP: " + BOLD + getClientIp(request) + RESET);
        System.out.println();
        
        log.warn("Route not found: {} {}", method, url);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("method", method);
        errorDetails.put("url", url);
        errorDetails.put("timestamp", System.currentTimeMillis());
        errorDetails.put("available_endpoints", getAvailableEndpoints());
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(ApiResponse.<Map<String, Object>>error(
                "Route not found: " + method + " " + url,
                "ROUTE_NOT_FOUND"));
    }
    
    /**
     * Handle resource not found (Spring Boot 3.x)
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleNoResourceFound(
            NoResourceFoundException ex, HttpServletRequest request) {
        
        String path = ex.getResourcePath();
        
        // Colored console output
        System.out.println();
        System.out.println(RED + BOLD + "🚫 RESOURCE NOT FOUND" + RESET);
        System.out.println(CYAN + "📁 Path: " + BOLD + path + RESET);
        System.out.println(CYAN + "🌐 Client IP: " + BOLD + getClientIp(request) + RESET);
        System.out.println();
        
        log.warn("Resource not found: {}", path);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("path", path);
        errorDetails.put("timestamp", System.currentTimeMillis());
        errorDetails.put("available_endpoints", getAvailableEndpoints());
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(ApiResponse.<Map<String, Object>>error(
                "Resource not found: " + path,
                "RESOURCE_NOT_FOUND"));
    }
    
    /**
     * Handle method not allowed (405 errors)
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleMethodNotSupported(
            HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {
        
        String method = ex.getMethod();
        String[] supportedMethods = ex.getSupportedMethods();
        String url = request.getRequestURI();
        
        // Colored console output
        System.out.println();
        System.out.println(YELLOW + BOLD + "⚠️  METHOD NOT ALLOWED" + RESET);
        System.out.println(CYAN + "📍 Method: " + BOLD + method + RESET);
        System.out.println(CYAN + "🔗 URL: " + BOLD + url + RESET);
        System.out.println(CYAN + "✅ Supported: " + BOLD + String.join(", ", supportedMethods) + RESET);
        System.out.println();
        
        log.warn("Method not allowed: {} {} - Supported: {}", method, url, supportedMethods);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("method", method);
        errorDetails.put("url", url);
        errorDetails.put("supported_methods", supportedMethods);
        errorDetails.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED)
            .body(ApiResponse.<Map<String, Object>>error(
                "Method " + method + " not allowed for " + url,
                "METHOD_NOT_ALLOWED"));
    }
    
    /**
     * Handle validation errors (400 errors)
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleValidationErrors(
            MethodArgumentNotValidException ex, HttpServletRequest request) {
        
        List<String> errors = ex.getBindingResult()
            .getFieldErrors()
            .stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.toList());
        
        String url = request.getRequestURI();
        
        // Colored console output
        System.out.println();
        System.out.println(YELLOW + BOLD + "⚠️  VALIDATION ERROR" + RESET);
        System.out.println(CYAN + "🔗 URL: " + BOLD + url + RESET);
        System.out.println(CYAN + "❌ Errors: " + BOLD + String.join(", ", errors) + RESET);
        System.out.println();
        
        log.warn("Validation errors for {}: {}", url, errors);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("url", url);
        errorDetails.put("validation_errors", errors);
        errorDetails.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(ApiResponse.<Map<String, Object>>error(
                "Validation failed",
                "VALIDATION_ERROR"));
    }
    
    /**
     * Handle general exceptions (500 errors)
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleGeneralException(
            Exception ex, HttpServletRequest request) {
        
        String url = request.getRequestURI();
        String method = request.getMethod();
        
        // Colored console output
        System.out.println();
        System.out.println(RED + BOLD + "💥 INTERNAL SERVER ERROR" + RESET);
        System.out.println(CYAN + "📍 Method: " + BOLD + method + RESET);
        System.out.println(CYAN + "🔗 URL: " + BOLD + url + RESET);
        System.out.println(CYAN + "🚨 Error: " + BOLD + ex.getMessage() + RESET);
        System.out.println();
        
        log.error("Internal server error for {} {}: {}", method, url, ex.getMessage(), ex);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("method", method);
        errorDetails.put("url", url);
        errorDetails.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponse.<Map<String, Object>>error(
                "Internal server error",
                "INTERNAL_ERROR"));
    }
    
    /**
     * Get client IP address
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * Get list of available endpoints
     */
    private Map<String, String> getAvailableEndpoints() {
        Map<String, String> endpoints = new HashMap<>();
        endpoints.put("health", "GET /api/v1/health");
        endpoints.put("auth_login", "POST /api/v1/auth/login");
        endpoints.put("auth_register", "POST /api/v1/auth/register/initiate");
        endpoints.put("user_profile", "GET /api/v1/users/me");
        endpoints.put("documentation", "Import AeTrust-API.postman_collection.json for complete API documentation");
        return endpoints;
    }
}
