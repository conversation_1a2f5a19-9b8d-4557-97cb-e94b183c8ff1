package com.aetrust.models;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_merchant_info", indexes = {
    @Index(name = "idx_merchant_info_user_id", columnList = "userId"),
    @Index(name = "idx_merchant_info_uuid", columnList = "userUuid"),
    @Index(name = "idx_merchant_info_api_key", columnList = "apiKey"),
    @Index(name = "idx_merchant_info_verification", columnList = "verificationStatus")
})
@EntityListeners(AuditingEntityListener.class)
public class UserMerchantInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false, unique = true)
    private Long userId;

    @Column(name = "user_uuid", nullable = false, unique = true)
    private UUID userUuid;

    @Column(name = "business_name")
    private String businessName;

    @Column(name = "business_type", length = 100)
    private String businessType;

    @Column(name = "business_registration", length = 100)
    private String businessRegistration;

    @Column(name = "tax_id", length = 100)
    private String taxId;

    @Column(name = "merchant_category", length = 100)
    private String merchantCategory;

    @Column(length = 255)
    private String website;

    @Column(name = "business_description", columnDefinition = "TEXT")
    private String businessDescription;

    @Column(name = "verification_status", length = 20)
    private String verificationStatus = "PENDING";

    @Column(name = "api_key", unique = true)
    private String apiKey;

    @Column(name = "webhook_url", length = 500)
    private String webhookUrl;

    @Column(name = "settlement_account", length = 100)
    private String settlementAccount;

    @CreatedDate
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    // helper methods
    public boolean isVerified() {
        return "VERIFIED".equals(verificationStatus);
    }

    public boolean isPending() {
        return "PENDING".equals(verificationStatus);
    }

    public boolean isRejected() {
        return "REJECTED".equals(verificationStatus);
    }

    public boolean hasApiAccess() {
        return isVerified() && apiKey != null && !apiKey.trim().isEmpty();
    }

    public boolean hasRequiredInfo() {
        return businessName != null && !businessName.trim().isEmpty() &&
               businessType != null && !businessType.trim().isEmpty() &&
               businessRegistration != null && !businessRegistration.trim().isEmpty() &&
               merchantCategory != null && !merchantCategory.trim().isEmpty();
    }

    public void generateApiKey() {
        this.apiKey = "ak_" + UUID.randomUUID().toString().replace("-", "");
    }

    public void revokeApiKey() {
        this.apiKey = null;
    }

    public boolean isWebhookConfigured() {
        return webhookUrl != null && !webhookUrl.trim().isEmpty();
    }
}
