package com.aetrust.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Component
public class RetryUtils {

    public static class RetryConfig {
        private int maxAttempts = 3;
        private long baseDelay = 1000;
        private long maxDelay = 30000;
        private double backoffMultiplier = 2.0;
        private boolean jitter = true;
        private List<String> retryableErrors = Arrays.asList(
            "timeout", "network", "connection", "service unavailable"
        );

        public static RetryConfig builder() {
            return new RetryConfig();
        }

        public RetryConfig maxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
            return this;
        }

        public RetryConfig baseDelay(long baseDelay) {
            this.baseDelay = baseDelay;
            return this;
        }

        public RetryConfig maxDelay(long maxDelay) {
            this.maxDelay = maxDelay;
            return this;
        }

        public RetryConfig backoffMultiplier(double backoffMultiplier) {
            this.backoffMultiplier = backoffMultiplier;
            return this;
        }

        public RetryConfig jitter(boolean jitter) {
            this.jitter = jitter;
            return this;
        }

        public RetryConfig retryableErrors(List<String> retryableErrors) {
            this.retryableErrors = retryableErrors;
            return this;
        }

        public RetryConfig retryableErrors(String... retryableErrors) {
            this.retryableErrors = Arrays.asList(retryableErrors);
            return this;
        }
    }

    public static class RetryableException extends RuntimeException {
        private final boolean shouldRetry;

        public RetryableException(String message) {
            this(message, true);
        }

        public RetryableException(String message, boolean shouldRetry) {
            super(message);
            this.shouldRetry = shouldRetry;
        }

        public RetryableException(String message, Throwable cause) {
            this(message, cause, true);
        }

        public RetryableException(String message, Throwable cause, boolean shouldRetry) {
            super(message, cause);
            this.shouldRetry = shouldRetry;
        }

        public boolean shouldRetry() {
            return shouldRetry;
        }
    }

    public static <T> T withRetry(Supplier<T> operation, RetryConfig config, String operationName) {
        Exception lastError = new RuntimeException("unknown error");

        for (int attempt = 1; attempt <= config.maxAttempts; attempt++) {
            try {
                T result = operation.get();

                if (attempt > 1) {
                    log.info("retry operation succeeded - operation: {}, attempt: {}, totalAttempts: {}",
                        operationName, attempt, config.maxAttempts);
                }

                return result;
            } catch (Exception error) {
                lastError = error;

                if (!isRetryableError(error, config.retryableErrors)) {
                    log.error("non-retryable error, stopping retry - operation: {}, attempt: {}, error: {}",
                        operationName, attempt, error.getMessage());
                    throw new RuntimeException(error);
                }

                if (attempt == config.maxAttempts) {
                    log.error("max retry attempts reached - operation: {}, attempt: {}, maxAttempts: {}, error: {}",
                        operationName, attempt, config.maxAttempts, error.getMessage());
                    break;
                }

                long delay = calculateDelay(attempt, config);

                log.warn("operation failed, retrying - operation: {}, attempt: {}, nextAttempt: {}, delayMs: {}, error: {}",
                    operationName, attempt, attempt + 1, delay, error.getMessage());

                try {
                    Thread.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("retry interrupted", ie);
                }
            }
        }

        throw new RuntimeException("retry failed after " + config.maxAttempts + " attempts", lastError);
    }

    public static <T> T withRetry(Supplier<T> operation, String operationName) {
        return withRetry(operation, RetryConfig.builder(), operationName);
    }

    public static <T> T withDatabaseRetry(Supplier<T> operation, String operationName) {
        RetryConfig config = RetryConfig.builder()
            .maxAttempts(3)
            .baseDelay(500)
            .maxDelay(5000)
            .retryableErrors("connection", "timeout", "network", "mongo", "duplicate key");
        
        return withRetry(operation, config, operationName);
    }

    public static <T> T withExternalApiRetry(Supplier<T> operation, String operationName) {
        RetryConfig config = RetryConfig.builder()
            .maxAttempts(5)
            .baseDelay(1000)
            .maxDelay(30000)
            .backoffMultiplier(2.0)
            .retryableErrors("timeout", "network", "rate limit", "service unavailable", 
                           "internal server error", "bad gateway", "gateway timeout");
        
        return withRetry(operation, config, operationName);
    }

    public static <T> T withPaymentRetry(Supplier<T> operation, String operationName) {
        RetryConfig config = RetryConfig.builder()
            .maxAttempts(3)
            .baseDelay(2000)
            .maxDelay(10000)
            .retryableErrors("timeout", "network", "service unavailable", "processing error");
        
        return withRetry(operation, config, operationName);
    }

    private static boolean isRetryableError(Exception error, List<String> retryableErrors) {
        if (error instanceof RetryableException) {
            return ((RetryableException) error).shouldRetry();
        }

        if (retryableErrors != null && !retryableErrors.isEmpty()) {
            String errorMessage = error.getMessage().toLowerCase();
            return retryableErrors.stream()
                .anyMatch(retryableError -> errorMessage.contains(retryableError.toLowerCase()));
        }

        List<String> defaultRetryableConditions = Arrays.asList(
            "timeout", "network", "connection", "econnreset", "enotfound", "econnrefused",
            "socket", "request timeout", "service unavailable", "internal server error",
            "bad gateway", "gateway timeout", "mongo", "database"
        );

        String errorMessage = error.getMessage().toLowerCase();
        return defaultRetryableConditions.stream()
            .anyMatch(condition -> errorMessage.contains(condition));
    }

    private static long calculateDelay(int attempt, RetryConfig config) {
        long delay = (long) (config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1));
        
        delay = Math.min(delay, config.maxDelay);
        
        if (config.jitter) {
            double jitterAmount = delay * 0.1; // 10% jitter
            delay += (long) ((ThreadLocalRandom.current().nextDouble() - 0.5) * 2 * jitterAmount);
        }
        
        return Math.max(delay, 0);
    }

    // Async versions for CompletableFuture operations
    public static <T> java.util.concurrent.CompletableFuture<T> withRetryAsync(
            Supplier<java.util.concurrent.CompletableFuture<T>> operation, 
            RetryConfig config, 
            String operationName) {
        
        return java.util.concurrent.CompletableFuture.supplyAsync(() -> {
            return withRetry(() -> operation.get().join(), config, operationName);
        });
    }

    public static <T> java.util.concurrent.CompletableFuture<T> withDatabaseRetryAsync(
            Supplier<java.util.concurrent.CompletableFuture<T>> operation, 
            String operationName) {
        
        RetryConfig config = RetryConfig.builder()
            .maxAttempts(3)
            .baseDelay(500)
            .maxDelay(5000)
            .retryableErrors("connection", "timeout", "network", "mongo", "duplicate key");
        
        return withRetryAsync(operation, config, operationName);
    }

    public static <T> java.util.concurrent.CompletableFuture<T> withExternalApiRetryAsync(
            Supplier<java.util.concurrent.CompletableFuture<T>> operation, 
            String operationName) {
        
        RetryConfig config = RetryConfig.builder()
            .maxAttempts(5)
            .baseDelay(1000)
            .maxDelay(30000)
            .backoffMultiplier(2.0)
            .retryableErrors("timeout", "network", "rate limit", "service unavailable", 
                           "internal server error", "bad gateway", "gateway timeout");
        
        return withRetryAsync(operation, config, operationName);
    }
}
