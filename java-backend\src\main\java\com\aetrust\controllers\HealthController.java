package com.aetrust.controllers;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/health")
public class HealthController {
    
    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        
        response.put("success", true);
        response.put("message", "AeTrust Backend is running");
        response.put("timestamp", LocalDateTime.now());
        response.put("version", "1.0.0");
        response.put("status", "ok");
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> status() {
        Map<String, Object> response = new HashMap<>();
        
        response.put("success", true);
        response.put("message", "Service is healthy");
        response.put("timestamp", LocalDateTime.now());
        response.put("uptime", "running");
        response.put("environment", "development");
        
        return ResponseEntity.ok(response);
    }
}
