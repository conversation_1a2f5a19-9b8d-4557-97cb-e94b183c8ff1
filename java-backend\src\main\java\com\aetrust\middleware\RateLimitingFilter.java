package com.aetrust.middleware;

import com.aetrust.config.RateLimitingConfig;
import com.aetrust.utils.JwtUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.ConsumptionProbe;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class RateLimitingFilter extends OncePerRequestFilter {

    @Autowired
    private RateLimitingConfig rateLimitingConfig;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        if (!rateLimitingConfig.isRateLimitingEnabled()) {
            filterChain.doFilter(request, response);
            return;
        }

        String clientIp = getClientIpAddress(request);
        String userId = extractUserIdFromToken(request);

        // check IP-based rate limit
        Bucket ipBucket = rateLimitingConfig.getIpBucket(clientIp);
        ConsumptionProbe ipProbe = ipBucket.tryConsumeAndReturnRemaining(1);

        if (!ipProbe.isConsumed()) {
            handleRateLimitExceeded(response, "IP rate limit exceeded", ipProbe);
            return;
        }

        // check user-based rate limit if user is authenticated
        if (userId != null) {
            Bucket userBucket = rateLimitingConfig.getUserBucket(userId);
            ConsumptionProbe userProbe = userBucket.tryConsumeAndReturnRemaining(1);

            if (!userProbe.isConsumed()) {
                handleRateLimitExceeded(response, "User rate limit exceeded", userProbe);
                return;
            }

            // add user rate limit headers
            response.addHeader("X-RateLimit-User-Remaining", String.valueOf(userProbe.getRemainingTokens()));
            response.addHeader("X-RateLimit-User-Reset", String.valueOf(userProbe.getNanosToWaitForRefill() / 1_000_000_000));
        }

        // add IP rate limit headers
        response.addHeader("X-RateLimit-IP-Remaining", String.valueOf(ipProbe.getRemainingTokens()));
        response.addHeader("X-RateLimit-IP-Reset", String.valueOf(ipProbe.getNanosToWaitForRefill() / 1_000_000_000));

        filterChain.doFilter(request, response);
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    private String extractUserIdFromToken(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7);
                JwtUtils.UserPayload payload = jwtUtils.extractUserPayload(token);
                return payload.getId();
            }
        } catch (Exception e) {
            // ignore token extraction errors for rate limiting
        }
        return null;
    }

    private void handleRateLimitExceeded(HttpServletResponse response, String message, 
                                       ConsumptionProbe probe) throws IOException {
        response.setStatus(429); // Too Many Requests
        response.setContentType("application/json");
        response.addHeader("X-RateLimit-Remaining", "0");
        response.addHeader("X-RateLimit-Reset", String.valueOf(probe.getNanosToWaitForRefill() / 1_000_000_000));
        response.addHeader("Retry-After", String.valueOf(probe.getNanosToWaitForRefill() / 1_000_000_000));

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", message);
        errorResponse.put("error_code", "RATE_LIMIT_EXCEEDED");
        errorResponse.put("retry_after", probe.getNanosToWaitForRefill() / 1_000_000_000);

        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));

        log.warn("Rate limit exceeded for request: {} from IP: {}", 
                 message, response.getHeader("X-Forwarded-For"));
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getRequestURI();
        // skip rate limiting for health checks and actuator endpoints
        return path.startsWith("/actuator/") || path.equals("/api/v1/health");
    }
}
