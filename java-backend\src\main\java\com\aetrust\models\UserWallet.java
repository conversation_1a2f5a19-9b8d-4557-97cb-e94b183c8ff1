package com.aetrust.models;

import com.aetrust.types.Types.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_wallets", indexes = {
    @Index(name = "idx_user_wallets_user_id", columnList = "userId"),
    @Index(name = "idx_user_wallets_uuid", columnList = "userUuid"),
    @Index(name = "idx_user_wallets_type", columnList = "walletType"),
    @Index(name = "idx_user_wallets_currency", columnList = "currency"),
    @Index(name = "idx_user_wallets_status", columnList = "status"),
    @Index(name = "idx_user_wallets_default", columnList = "isDefault")
})
@EntityListeners(AuditingEntityListener.class)
public class UserWallet {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "user_uuid", nullable = false)
    private UUID userUuid;

    @Enumerated(EnumType.STRING)
    @Column(name = "wallet_type", nullable = false)
    private WalletType walletType = WalletType.MAIN;

    @Column(nullable = false, length = 10)
    private String currency = "USD";

    @Column(precision = 15, scale = 2)
    private BigDecimal balance = BigDecimal.ZERO;

    @Column(name = "available_balance", precision = 15, scale = 2)
    private BigDecimal availableBalance = BigDecimal.ZERO;

    @Column(name = "pending_balance", precision = 15, scale = 2)
    private BigDecimal pendingBalance = BigDecimal.ZERO;

    @Column(name = "wallet_address", unique = true, length = 255)
    private String walletAddress;

    @Column(name = "is_default")
    private boolean isDefault = true;

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private WalletStatus status = WalletStatus.ACTIVE;

    @Column(name = "daily_limit", precision = 15, scale = 2)
    private BigDecimal dailyLimit = new BigDecimal("10000.00");

    @Column(name = "monthly_limit", precision = 15, scale = 2)
    private BigDecimal monthlyLimit = new BigDecimal("100000.00");

    @Column(name = "transaction_limit", precision = 15, scale = 2)
    private BigDecimal transactionLimit = new BigDecimal("5000.00");

    @Column(name = "last_transaction_date")
    private LocalDateTime lastTransactionDate;

    @Column(name = "total_transactions")
    private Integer totalTransactions = 0;

    @Column(name = "total_credited", precision = 15, scale = 2)
    private BigDecimal totalCredited = BigDecimal.ZERO;

    @Column(name = "total_debited", precision = 15, scale = 2)
    private BigDecimal totalDebited = BigDecimal.ZERO;

    @CreatedDate
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    public boolean canTransact() {
        return status == WalletStatus.ACTIVE && deletedAt == null;
    }

    public boolean hasInsufficientFunds(BigDecimal amount) {
        return availableBalance.compareTo(amount) < 0;
    }

    public boolean exceedsTransactionLimit(BigDecimal amount) {
        return amount.compareTo(transactionLimit) > 0;
    }

    public void updateBalance(BigDecimal amount, boolean isCredit) {
        if (isCredit) {
            balance = balance.add(amount);
            availableBalance = availableBalance.add(amount);
            totalCredited = totalCredited.add(amount);
        } else {
            balance = balance.subtract(amount);
            availableBalance = availableBalance.subtract(amount);
            totalDebited = totalDebited.add(amount);
        }
        totalTransactions++;
        lastTransactionDate = LocalDateTime.now();
    }

    public void freezeAmount(BigDecimal amount) {
        availableBalance = availableBalance.subtract(amount);
        pendingBalance = pendingBalance.add(amount);
    }

    public void unfreezeAmount(BigDecimal amount) {
        availableBalance = availableBalance.add(amount);
        pendingBalance = pendingBalance.subtract(amount);
    }
}
