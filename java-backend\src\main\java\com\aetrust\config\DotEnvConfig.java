package com.aetrust.config;

import io.github.cdimascio.dotenv.Dotenv;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

@Slf4j
@Configuration
public class DotEnvConfig {

    @PostConstruct
    public void loadDotEnv() {
        try {
            Dotenv dotenv = Dotenv.configure()
                    .directory("./")
                    .filename(".env")
                    .ignoreIfMalformed()
                    .ignoreIfMissing()
                    .load();

            dotenv.entries().forEach(entry -> {
                String key = entry.getKey();
                String value = entry.getValue();

                // Set system properties to make them available to Spring
                System.setProperty(key, value);
                log.info("Loaded env variable: {} = {}", key, maskSensitiveValue(key, value));
            });
            
            log.info("Successfully loaded .env file with {} variables", dotenv.entries().size());
            
        } catch (Exception e) {
            log.warn("Could not load .env file: {}", e.getMessage());
        }
    }
    
    private String maskSensitiveValue(String key, String value) {
        String lowerKey = key.toLowerCase();
        if (lowerKey.contains("password") || lowerKey.contains("secret") || 
            lowerKey.contains("key") || lowerKey.contains("token")) {
            return "****";
        }
        return value;
    }
}
