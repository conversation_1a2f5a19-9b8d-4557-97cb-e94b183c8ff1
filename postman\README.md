# AeTrust Backend API Collections

This directory contains comprehensive Postman collections for testing both the TypeScript and Java backends of the AeTrust financial platform.

## Collections Overview

### 1. AeTrust Java Backend API (`AeTrust-Java-Backend-API.postman_collection.json`)
**Primary Collection** - Complete testing suite for the Java backend with modular user architecture.

**Base URL**: `http://localhost:8080/api/v1`

**Features**:
- ✅ Complete registration flow (8 steps)
- ✅ Modular user management operations
- ✅ Dashboard endpoints with enhanced data
- ✅ Wallet and security operations
- ✅ Automatic token and UUID management
- ✅ Production-grade endpoint testing

### 2. AeTrust TypeScript Backend API (`AeTrust-TypeScript-Backend-API.postman_collection.json`)
**Reference Collection** - Basic testing for the original TypeScript backend.

**Base URL**: `http://localhost:3000/api/v1`

**Features**:
- ✅ Basic authentication flow
- ✅ User management operations
- ✅ Dashboard endpoints
- ✅ Compatible with existing frontend

## Quick Start Guide

### Prerequisites
1. **Java Backend**: Ensure Java backend is running on port 8080
2. **TypeScript Backend**: Ensure TypeScript backend is running on port 3000
3. **Database**: PostgreSQL for Java, MongoDB for TypeScript
4. **Postman**: Import the collections into Postman

### Import Collections
1. Open Postman
2. Click "Import" button
3. Select the JSON files from this directory
4. Collections will be imported with all variables and tests

### Environment Variables
Both collections use these variables (automatically managed):

| Variable | Description | Auto-Set |
|----------|-------------|----------|
| `baseUrl` | Backend base URL | ✅ |
| `authToken` | JWT authentication token | ✅ |
| `userUuid` | User UUID (Java) | ✅ |
| `userId` | User ID (TypeScript) | ✅ |
| `registrationId` | Registration session ID | ✅ |
| `walletId` | Wallet identifier | ✅ |

## Java Backend Testing Flow

### 1. Complete Registration Flow
Execute requests in order:

1. **Initiate Registration** → Sets `registrationId`
2. **Verify Phone** → Sets `authToken` and `userUuid`
3. **Verify Email** → Confirms email verification
4. **Complete Personal Info** → Adds profile data
5. **Submit Identity Verification** → KYC documents
6. **Set Transaction PIN** → Security setup
7. **Enable Biometric** → Biometric enrollment
8. **Business Verification** → For agents/merchants (optional)

### 2. User Management Operations
After registration:

- **Get Current User** → Complete modular user data
- **Update Profile** → Modify user information
- **Update Password** → Change password
- **Upload Profile Picture** → Image upload
- **Search Users** → Find other users
- **Get User Stats** → Analytics data

### 3. Dashboard Operations
Rich dashboard data:

- **Get Basic Dashboard** → Core dashboard data
- **Get Enhanced Dashboard** → Analytics + recommendations
- **Get Dashboard Profile** → Profile-specific data
- **Get Wallet Summary** → Financial overview
- **Get Account Status** → Verification levels

### 4. Modular Operations
Direct modular architecture testing:

- **Create User Wallet** → New wallet creation
- **Update Wallet Balance** → Balance modifications
- **Update Security Settings** → Security configuration
- **Update User Preferences** → User settings

## Key Differences: Java vs TypeScript

### Architecture
| Aspect | Java Backend | TypeScript Backend |
|--------|--------------|-------------------|
| **Database** | PostgreSQL (Modular) | MongoDB (Monolithic) |
| **User Model** | 8 Linked Tables | Single Collection |
| **Performance** | Optimized Queries | Basic Queries |
| **Scalability** | Enterprise-Grade | Standard |
| **Security** | Enhanced | Basic |

### Response Structure
**Java Backend** (Enhanced):
```json
{
  "success": true,
  "message": "user profile retrieved successfully",
  "data": {
    "user": { /* core user data */ },
    "profile": { /* profile data */ },
    "wallets": [ /* wallet array */ ],
    "security": { /* security settings */ },
    "preferences": { /* user preferences */ },
    "identity": { /* KYC data */ },
    "agentInfo": { /* agent data */ },
    "merchantInfo": { /* merchant data */ }
  }
}
```

**TypeScript Backend** (Standard):
```json
{
  "success": true,
  "message": "user profile retrieved successfully",
  "data": {
    "id": "user_id",
    "email": "<EMAIL>",
    "phone": "+250788123456",
    /* ... flat user data ... */
  }
}
```

## Testing Scenarios

### 1. Registration Flow Testing
- Test complete 8-step registration
- Verify data persistence across steps
- Test error handling and validation
- Verify token management

### 2. User Operations Testing
- Test CRUD operations on user data
- Verify modular data updates
- Test authentication and authorization
- Verify data consistency

### 3. Dashboard Testing
- Test different dashboard views
- Verify data aggregation
- Test performance with large datasets
- Verify real-time updates

### 4. Security Testing
- Test authentication flows
- Verify rate limiting
- Test input validation
- Verify data sanitization

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Ensure tokens are properly set
   - Check token expiration
   - Verify user permissions

2. **Connection Errors**
   - Verify backend is running
   - Check port configurations
   - Ensure database connectivity

3. **Validation Errors**
   - Check request body format
   - Verify required fields
   - Validate data types

### Debug Tips

1. **Enable Postman Console** → View detailed request/response logs
2. **Check Variables** → Ensure auto-set variables are populated
3. **Test Scripts** → Review test script outputs
4. **Network Tab** → Monitor actual HTTP requests

## Advanced Usage

### Custom Test Scripts
Collections include automatic test scripts for:
- Token extraction and storage
- UUID management
- Response validation
- Error handling

### Batch Testing
Use Postman Runner for:
- Complete flow testing
- Load testing
- Regression testing
- CI/CD integration

### Environment Management
- **Development**: Local testing
- **Staging**: Pre-production testing
- **Production**: Live system testing (use with caution)

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review the backend logs
3. Verify database connectivity
4. Test individual endpoints

## Updates

Collections are maintained alongside backend development:
- **Version 2.0.0**: Java backend with modular architecture
- **Version 1.0.0**: TypeScript backend compatibility

Last updated: Current development cycle
