package com.aetrust.services;

import com.aetrust.utils.RetryUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class NotificationService {
    
    @Value("${aetrust.notification.sms.provider:twilio}")
    private String smsProvider;
    
    @Value("${aetrust.notification.sms.api-key:}")
    private String smsApiKey;
    
    @Value("${aetrust.notification.email.provider:sendgrid}")
    private String emailProvider;
    
    @Value("${aetrust.notification.email.api-key:}")
    private String emailApiKey;
    
    public boolean sendSMS(String phoneNumber, String message) {
        try {
            log.info("SMS sent to {}: {}", maskPhoneNumber(phoneNumber), message);

            return RetryUtils.withExternalApiRetry(() -> {
                return simulateSMSSending(phoneNumber, message);
            }, "sms-send");
            
        } catch (Exception error) {
            log.error("Failed to send SMS to {}: {}", maskPhoneNumber(phoneNumber), error.getMessage());
            return false;
        }
    }
    
    public boolean sendEmail(String email, String subject, String message) {
        try {
            
            log.info("Email sent to {}: {} - {}", maskEmail(email), subject, message);

            return RetryUtils.withExternalApiRetry(() -> {
                return simulateEmailSending(email, subject, message);
            }, "email-send");
            
        } catch (Exception error) {
            log.error("Failed to send email to {}: {}", maskEmail(email), error.getMessage());
            return false;
        }
    }
    
    private boolean simulateSMSSending(String phoneNumber, String message) {
        if (smsApiKey == null || smsApiKey.isEmpty()) {
            log.warn("SMS API key not configured, SMS not sent");
            return false;
        }
        
        return true;
    }
    
    private boolean simulateEmailSending(String email, String subject, String message) {
        if (emailApiKey == null || emailApiKey.isEmpty()) {
            log.warn("Email API key not configured, email not sent");
            return false;
        }
        
        return true;
    }
    
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return "****";
        }
        
        String start = phoneNumber.substring(0, 3);
        String end = phoneNumber.substring(phoneNumber.length() - 2);
        String middle = "*".repeat(phoneNumber.length() - 5);
        
        return start + middle + end;
    }
    
    private String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return "****@****.***";
        }
        
        String[] parts = email.split("@");
        String localPart = parts[0];
        String domain = parts[1];
        
        String maskedLocal = localPart.length() > 2 ? 
            localPart.substring(0, 2) + "*".repeat(localPart.length() - 2) : 
            "**";
            
        return maskedLocal + "@" + domain;
    }
}
