package com.aetrust.types;

public class Types {
    
    public enum UserRole {
        USER("user"),
        AGENT("agent"),
        ADMIN("admin"),
        SUPER_ADMIN("super_admin");
        
        private final String value;
        
        UserRole(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
    
    public enum KycStatus {
        PENDING("pending"),
        UNDER_REVIEW("under_review"),
        APPROVED("approved"),
        REJECTED("rejected"),
        EXPIRED("expired");
        
        private final String value;
        
        KycStatus(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
    
    public enum AccountStatus {
        ACTIVE("active"),
        INACTIVE("inactive"),
        SUSPENDED("suspended"),
        CLOSED("closed"),
        PENDING_VERIFICATION("pending_verification");
        
        private final String value;
        
        AccountStatus(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
    
    public enum RegistrationStep {
        PHONE_VERIFICATION("phone_verification"),
        EMAIL_VERIFICATION("email_verification"),
        PERSONAL_INFO("personal_info"),
        IDENTITY_VERIFICATION("identity_verification"),
        TRANSACTION_PIN("transaction_pin"),
        COMPLETED("completed");
        
        private final String value;
        
        RegistrationStep(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
    
    public enum VerificationStatus {
        PENDING("pending"),
        UNDER_REVIEW("under_review"),
        APPROVED("approved"),
        REJECTED("rejected"),
        EXPIRED("expired");
        
        private final String value;
        
        VerificationStatus(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
    
    public enum IdType {
        NATIONAL_ID("national_id"),
        PASSPORT("passport"),
        DRIVERS_LICENSE("drivers_license"),
        VOTERS_CARD("voters_card");
        
        private final String value;
        
        IdType(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
    
    public enum AgentStatus {
        PENDING("pending"),
        UNDER_REVIEW("under_review"),
        APPROVED("approved"),
        REJECTED("rejected"),
        SUSPENDED("suspended"),
        ACTIVE("active"),
        INACTIVE("inactive");
        
        private final String value;
        
        AgentStatus(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
    
    public enum TransactionType {
        DEPOSIT("deposit"),
        WITHDRAWAL("withdrawal"),
        TRANSFER("transfer"),
        PAYMENT("payment"),
        REFUND("refund"),
        FEE("fee"),
        COMMISSION("commission"),
        LOAN_DISBURSEMENT("loan_disbursement"),
        LOAN_REPAYMENT("loan_repayment"),
        SAVINGS_DEPOSIT("savings_deposit"),
        SAVINGS_WITHDRAWAL("savings_withdrawal"),
        CASH_IN("cash_in"),
        CASH_OUT("cash_out");
        
        private final String value;
        
        TransactionType(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
    
    public enum TransactionStatus {
        PENDING("pending"),
        PROCESSING("processing"),
        COMPLETED("completed"),
        FAILED("failed"),
        CANCELLED("cancelled"),
        REVERSED("reversed");
        
        private final String value;
        
        TransactionStatus(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
    
    public enum Currency {
        USD("USD"),
        EUR("EUR"),
        GBP("GBP"),
        RWF("RWF"),
        KES("KES"),
        UGX("UGX"),
        TZS("TZS");
        
        private final String value;
        
        Currency(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
    
    public enum PaymentMethod {
        WALLET("wallet"),
        BANK_TRANSFER("bank_transfer"),
        MOBILE_MONEY("mobile_money"),
        CARD("card"),
        CASH("cash"),
        CRYPTO("crypto");
        
        private final String value;
        
        PaymentMethod(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return value;
        }
    }
}
