package com.aetrust.controllers;

import com.aetrust.config.DatabaseConfig;
import com.aetrust.services.DatabaseService;
import com.aetrust.services.PostgresService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/database", "/public"})
public class DatabaseHealthController {
    
    private final DatabaseService databaseService;
    private final PostgresService postgresService;
    private final DatabaseConfig databaseConfig;
    
    @Autowired
    public DatabaseHealthController(
            DatabaseService databaseService,
            PostgresService postgresService,
            DatabaseConfig databaseConfig) {
        this.databaseService = databaseService;
        this.postgresService = postgresService;
        this.databaseConfig = databaseConfig;
    }
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> checkDatabaseHealth() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            boolean isHealthy = databaseService.isHealthy();
            long responseTime = System.currentTimeMillis() - startTime;
            
            response.put("success", isHealthy);
            response.put("message", isHealthy ? "Database is healthy" : "Database health check failed");
            response.put("timestamp", LocalDateTime.now());
            response.put("response_time_ms", responseTime);
            response.put("database_name", databaseConfig.getDatabaseName());
            response.put("database_host", databaseConfig.getDatabaseHost());
            response.put("database_port", databaseConfig.getDatabasePort());
            
            if (isHealthy) {
                // get connection pool stats
                Map<String, Object> poolStats = databaseService.getConnectionPoolStats();
                response.put("connection_pool", poolStats);
                
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
            }
            
        } catch (Exception e) {
            log.error("Database health check failed", e);
            response.put("success", false);
            response.put("message", "Database health check error: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
        }
    }
    
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getDatabaseInfo() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> dbInfo = postgresService.getConnectionInfo();
            Map<String, Object> poolStats = databaseService.getConnectionPoolStats();
            
            response.put("success", true);
            response.put("message", "Database info retrieved successfully");
            response.put("timestamp", LocalDateTime.now());
            response.put("database_info", dbInfo);
            response.put("connection_pool_stats", poolStats);
            response.put("configuration", Map.of(
                "max_pool_size", databaseConfig.getHikari().getMaximumPoolSize(),
                "min_idle", databaseConfig.getHikari().getMinimumIdle(),
                "connection_timeout", databaseConfig.getHikari().getConnectionTimeout(),
                "idle_timeout", databaseConfig.getHikari().getIdleTimeout(),
                "max_lifetime", databaseConfig.getHikari().getMaxLifetime(),
                "auto_commit", databaseConfig.getHikari().isAutoCommit()
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (SQLException e) {
            log.error("Failed to get database info", e);
            response.put("success", false);
            response.put("message", "Failed to retrieve database info: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            response.put("sql_state", e.getSQLState());
            response.put("error_code", e.getErrorCode());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            
        } catch (Exception e) {
            log.error("Unexpected error getting database info", e);
            response.put("success", false);
            response.put("message", "Unexpected error: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testDatabaseConnection() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            
            Map<String, Object> connectionInfo = postgresService.getConnectionInfo();

            var testResults = postgresService.executeCustomQuery("SELECT 1 as test_value, NOW() as current_time");
            
            long responseTime = System.currentTimeMillis() - startTime;
            
            response.put("success", true);
            response.put("message", "Database connection test successful");
            response.put("timestamp", LocalDateTime.now());
            response.put("response_time_ms", responseTime);
            response.put("connection_info", connectionInfo);
            response.put("test_query_result", testResults);
            
            return ResponseEntity.ok(response);
            
        } catch (SQLException e) {
            log.error("Database connection test failed", e);
            response.put("success", false);
            response.put("message", "Database connection test failed: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            response.put("sql_state", e.getSQLState());
            response.put("error_code", e.getErrorCode());
            
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
            
        } catch (Exception e) {
            log.error("Unexpected error in connection test", e);
            response.put("success", false);
            response.put("message", "Connection test error: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/tables")
    public ResponseEntity<Map<String, Object>> listDatabaseTables() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // query to get all tables in public schema
            String sql = """
                SELECT table_name, table_type, table_schema
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
                """;
            
            var tables = postgresService.executeCustomQuery(sql);
            
            response.put("success", true);
            response.put("message", "Database tables retrieved successfully");
            response.put("timestamp", LocalDateTime.now());
            response.put("table_count", tables.size());
            response.put("tables", tables);
            
            return ResponseEntity.ok(response);
            
        } catch (SQLException e) {
            log.error("Failed to list database tables", e);
            response.put("success", false);
            response.put("message", "Failed to list tables: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            response.put("sql_state", e.getSQLState());
            response.put("error_code", e.getErrorCode());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            
        } catch (Exception e) {
            log.error("Unexpected error listing tables", e);
            response.put("success", false);
            response.put("message", "Error listing tables: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getDatabaseStats() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // get database statistics
            String statsSql = """
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    correlation
                FROM pg_stats 
                WHERE schemaname = 'public'
                LIMIT 20
                """;
            
            String sizeSql = """
                SELECT 
                    pg_size_pretty(pg_database_size(current_database())) as database_size,
                    current_database() as database_name
                """;
            
            var stats = postgresService.executeCustomQuery(statsSql);
            var sizeInfo = postgresService.executeCustomQuery(sizeSql);
            
            response.put("success", true);
            response.put("message", "Database statistics retrieved successfully");
            response.put("timestamp", LocalDateTime.now());
            response.put("table_statistics", stats);
            response.put("database_size_info", sizeInfo);
            
            return ResponseEntity.ok(response);
            
        } catch (SQLException e) {
            log.error("Failed to get database stats", e);
            response.put("success", false);
            response.put("message", "Failed to get database stats: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            response.put("sql_state", e.getSQLState());
            response.put("error_code", e.getErrorCode());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            
        } catch (Exception e) {
            log.error("Unexpected error getting database stats", e);
            response.put("success", false);
            response.put("message", "Error getting database stats: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // Public endpoints (no authentication required)
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getPublicStatus() {
        Map<String, Object> response = new HashMap<>();

        response.put("success", true);
        response.put("message", "AeTrust Backend is running");
        response.put("timestamp", LocalDateTime.now());
        response.put("version", "1.0.0");
        response.put("environment", "development");

        return ResponseEntity.ok(response);
    }

    @GetMapping("/db-test")
    public ResponseEntity<Map<String, Object>> publicDatabaseTest() {
        Map<String, Object> response = new HashMap<>();

        try {
            var results = postgresService.executeCustomQuery("SELECT 1 as test_value, NOW() as current_time, version() as db_version");

            response.put("success", true);
            response.put("message", "Database test successful");
            response.put("timestamp", LocalDateTime.now());
            response.put("test_results", results);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Database test failed", e);
            response.put("success", false);
            response.put("message", "Database test failed: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            response.put("error", e.getClass().getSimpleName());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
