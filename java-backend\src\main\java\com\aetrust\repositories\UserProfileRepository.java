package com.aetrust.repositories;

import com.aetrust.models.User.UserProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserProfileRepository extends JpaRepository<UserProfile, Long> {
    
    // Find by user references
    Optional<UserProfile> findByUserIdAndDeletedAtIsNull(Long userId);
    Optional<UserProfile> findByUserUuidAndDeletedAtIsNull(UUID userUuid);
    
    // Existence checks
    boolean existsByUserIdAndDeletedAtIsNull(Long userId);
    boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);
    
    // Find by profile fields
    List<UserProfile> findByFirstNameContainingIgnoreCaseAndDeletedAtIsNull(String firstName);
    List<UserProfile> findByLastNameContainingIgnoreCaseAndDeletedAtIsNull(String lastName);
    List<UserProfile> findByCountryAndDeletedAtIsNull(String country);
    List<UserProfile> findByCityAndDeletedAtIsNull(String city);
    
    // Search functionality
    @Query("SELECT p FROM UserProfile p WHERE " +
           "(LOWER(p.firstName) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(p.lastName) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(p.city) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(p.country) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "AND p.deletedAt IS NULL")
    List<UserProfile> searchProfiles(@Param("search") String search);
    
    // Find profiles with complete information
    @Query("SELECT p FROM UserProfile p WHERE " +
           "p.firstName IS NOT NULL AND p.lastName IS NOT NULL " +
           "AND p.dateOfBirth IS NOT NULL AND p.deletedAt IS NULL")
    List<UserProfile> findCompleteProfiles();
    
    // Find profiles missing information
    @Query("SELECT p FROM UserProfile p WHERE " +
           "(p.firstName IS NULL OR p.lastName IS NULL OR p.dateOfBirth IS NULL) " +
           "AND p.deletedAt IS NULL")
    List<UserProfile> findIncompleteProfiles();
    
    // Soft delete
    @Modifying
    @Query("UPDATE UserProfile p SET p.deletedAt = :deletedAt WHERE p.userId = :userId")
    void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);
    
    @Modifying
    @Query("UPDATE UserProfile p SET p.deletedAt = :deletedAt WHERE p.userUuid = :userUuid")
    void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);
}
