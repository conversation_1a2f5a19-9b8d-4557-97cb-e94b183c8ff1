package com.aetrust.repositories;

import com.aetrust.models.User;
import com.aetrust.types.Types.AccountStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    Optional<User> findByEmailAndDeletedAtIsNull(String email);
    Optional<User> findByPhoneAndDeletedAtIsNull(String phone);
    Optional<User> findByUsernameAndDeletedAtIsNull(String username);
    Optional<User> findByEmailAndDeletedAtIsNullAndAccountStatusNot(String email, AccountStatus accountStatus);
    Optional<User> findByPhoneAndDeletedAtIsNullAndAccountStatusNot(String phone, AccountStatus accountStatus);
    boolean existsByEmailAndDeletedAtIsNull(String email);
    boolean existsByPhoneAndDeletedAtIsNull(String phone);
    boolean existsByUsernameAndDeletedAtIsNull(String username);
    List<User> findByAccountStatusAndDeletedAtIsNull(AccountStatus accountStatus);
    List<User> findByCreatedAtAfterAndDeletedAtIsNull(LocalDateTime date);
    List<User> findByIsVerifiedAndDeletedAtIsNull(boolean isVerified);
    
    @Query("SELECT u FROM User u WHERE u.walletBalance > :amount AND u.deletedAt IS NULL")
    List<User> findUsersWithBalanceGreaterThan(@Param("amount") Double amount);
    
    @Query("SELECT u FROM User u WHERE u.role = :role AND u.deletedAt IS NULL")
    List<User> findUsersByRole(@Param("role") String role);
    
    @Query("UPDATE User u SET u.deletedAt = :deletedAt WHERE u.id = :id")
    void softDeleteById(@Param("id") Long id, @Param("deletedAt") LocalDateTime deletedAt);
    
    @Query("SELECT u FROM User u WHERE u.deletedAt IS NULL ORDER BY u.createdAt DESC")
    List<User> findAllActiveUsers();
    
    @Query("SELECT u FROM User u WHERE (LOWER(u.firstName) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(u.lastName) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(u.email) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "AND u.deletedAt IS NULL")
    List<User> searchUsers(@Param("search") String search);
}
