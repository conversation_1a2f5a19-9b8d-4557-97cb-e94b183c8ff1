package com.aetrust.repositories;

import com.aetrust.models.User;
import com.aetrust.types.Types.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    Optional<User> findByEmailAndDeletedAtIsNull(String email);
    Optional<User> findByPhoneAndDeletedAtIsNull(String phone);
    Optional<User> findByUsernameAndDeletedAtIsNull(String username);
    Optional<User> findByUserUuidAndDeletedAtIsNull(UUID userUuid);
    Optional<User> findByEmailAndDeletedAtIsNullAndAccountStatusNot(String email, AccountStatus accountStatus);
    Optional<User> findByPhoneAndDeletedAtIsNullAndAccountStatusNot(String phone, AccountStatus accountStatus);

    boolean existsByEmailAndDeletedAtIsNull(String email);
    boolean existsByPhoneAndDeletedAtIsNull(String phone);
    boolean existsByUsernameAndDeletedAtIsNull(String username);
    boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);

    List<User> findByAccountStatusAndDeletedAtIsNull(AccountStatus accountStatus);
    List<User> findByKycStatusAndDeletedAtIsNull(KycStatus kycStatus);
    List<User> findByRoleAndDeletedAtIsNull(UserRole role);
    List<User> findByIsVerifiedAndDeletedAtIsNull(boolean isVerified);
    List<User> findByRegistrationStepAndDeletedAtIsNull(RegistrationStep registrationStep);

    List<User> findByCreatedAtAfterAndDeletedAtIsNull(LocalDateTime date);
    List<User> findByCreatedAtBetweenAndDeletedAtIsNull(LocalDateTime startDate, LocalDateTime endDate);

    @Query("SELECT u FROM User u WHERE u.role = :role AND u.deletedAt IS NULL")
    List<User> findUsersByRole(@Param("role") UserRole role);

    @Query("SELECT u FROM User u WHERE u.deletedAt IS NULL ORDER BY u.createdAt DESC")
    List<User> findAllActiveUsers();

    @Query("SELECT u FROM User u WHERE " +
           "(LOWER(u.email) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(u.phone) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(u.username) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "AND u.deletedAt IS NULL")
    List<User> searchUsers(@Param("search") String search);

    @Query("SELECT COUNT(u) FROM User u WHERE u.role = :role AND u.deletedAt IS NULL")
    long countByRole(@Param("role") UserRole role);

    @Query("SELECT COUNT(u) FROM User u WHERE u.accountStatus = :status AND u.deletedAt IS NULL")
    long countByAccountStatus(@Param("status") AccountStatus status);

    @Query("SELECT COUNT(u) FROM User u WHERE u.kycStatus = :status AND u.deletedAt IS NULL")
    long countByKycStatus(@Param("status") KycStatus status);

    // Soft delete
    @Modifying
    @Query("UPDATE User u SET u.deletedAt = :deletedAt WHERE u.id = :id")
    void softDeleteById(@Param("id") Long id, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User u SET u.deletedAt = :deletedAt WHERE u.userUuid = :userUuid")
    void softDeleteByUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);


    @Repository
    interface UserProfileRepository extends JpaRepository<User.UserProfile, Long> {

        Optional<User.UserProfile> findByUserIdAndDeletedAtIsNull(Long userId);
        Optional<User.UserProfile> findByUserUuidAndDeletedAtIsNull(UUID userUuid);

        boolean existsByUserIdAndDeletedAtIsNull(Long userId);
        boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);

        List<User.UserProfile> findByFirstNameContainingIgnoreCaseAndDeletedAtIsNull(String firstName);
        List<User.UserProfile> findByLastNameContainingIgnoreCaseAndDeletedAtIsNull(String lastName);
        List<User.UserProfile> findByCountryAndDeletedAtIsNull(String country);
        List<User.UserProfile> findByCityAndDeletedAtIsNull(String city);

        @Query("SELECT p FROM UserProfile p WHERE " +
               "(LOWER(p.firstName) LIKE LOWER(CONCAT('%', :search, '%')) " +
               "OR LOWER(p.lastName) LIKE LOWER(CONCAT('%', :search, '%')) " +
               "OR LOWER(p.city) LIKE LOWER(CONCAT('%', :search, '%')) " +
               "OR LOWER(p.country) LIKE LOWER(CONCAT('%', :search, '%'))) " +
               "AND p.deletedAt IS NULL")
        List<User.UserProfile> searchProfiles(@Param("search") String search);

        @Query("SELECT p FROM UserProfile p WHERE " +
               "p.firstName IS NOT NULL AND p.lastName IS NOT NULL " +
               "AND p.dateOfBirth IS NOT NULL AND p.deletedAt IS NULL")
        List<User.UserProfile> findCompleteProfiles();

        @Query("SELECT p FROM UserProfile p WHERE " +
               "(p.firstName IS NULL OR p.lastName IS NULL OR p.dateOfBirth IS NULL) " +
               "AND p.deletedAt IS NULL")
        List<User.UserProfile> findIncompleteProfiles();

        @Modifying
        @Query("UPDATE UserProfile p SET p.deletedAt = :deletedAt WHERE p.userId = :userId")
        void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

        @Modifying
        @Query("UPDATE UserProfile p SET p.deletedAt = :deletedAt WHERE p.userUuid = :userUuid")
        void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);
    }

    @Repository
    interface UserWalletRepository extends JpaRepository<User.UserWallet, Long> {

        List<User.UserWallet> findByUserIdAndDeletedAtIsNull(Long userId);
        List<User.UserWallet> findByUserUuidAndDeletedAtIsNull(UUID userUuid);

        Optional<User.UserWallet> findByUserIdAndIsDefaultTrueAndDeletedAtIsNull(Long userId);
        Optional<User.UserWallet> findByUserUuidAndIsDefaultTrueAndDeletedAtIsNull(UUID userUuid);

        Optional<User.UserWallet> findByUserIdAndWalletTypeAndCurrencyAndDeletedAtIsNull(
            Long userId, WalletType walletType, String currency);
        Optional<User.UserWallet> findByUserUuidAndWalletTypeAndCurrencyAndDeletedAtIsNull(
            UUID userUuid, WalletType walletType, String currency);

        List<User.UserWallet> findByUserIdAndStatusAndDeletedAtIsNull(Long userId, WalletStatus status);
        List<User.UserWallet> findByStatusAndDeletedAtIsNull(WalletStatus status);

        Optional<User.UserWallet> findByWalletAddressAndDeletedAtIsNull(String walletAddress);

        @Query("SELECT w FROM UserWallet w WHERE w.userId = :userId AND w.balance >= :minBalance AND w.deletedAt IS NULL")
        List<User.UserWallet> findByUserIdAndBalanceGreaterThanEqual(@Param("userId") Long userId, @Param("minBalance") BigDecimal minBalance);

        @Query("SELECT w FROM UserWallet w WHERE w.balance >= :minBalance AND w.deletedAt IS NULL")
        List<User.UserWallet> findByBalanceGreaterThanEqual(@Param("minBalance") BigDecimal minBalance);

        @Query("SELECT SUM(w.balance) FROM UserWallet w WHERE w.userId = :userId AND w.status = :status AND w.deletedAt IS NULL")
        BigDecimal getTotalBalanceByUserIdAndStatus(@Param("userId") Long userId, @Param("status") WalletStatus status);

        @Query("SELECT SUM(w.availableBalance) FROM UserWallet w WHERE w.userId = :userId AND w.status = :status AND w.deletedAt IS NULL")
        BigDecimal getTotalAvailableBalanceByUserIdAndStatus(@Param("userId") Long userId, @Param("status") WalletStatus status);

        @Query("SELECT w FROM UserWallet w WHERE w.totalTransactions >= :minTransactions AND w.deletedAt IS NULL")
        List<User.UserWallet> findByTotalTransactionsGreaterThanEqual(@Param("minTransactions") Integer minTransactions);

        @Query("SELECT w FROM UserWallet w WHERE w.lastTransactionDate >= :date AND w.deletedAt IS NULL")
        List<User.UserWallet> findByLastTransactionDateAfter(@Param("date") LocalDateTime date);

        @Query("SELECT w.currency, COUNT(w) FROM UserWallet w WHERE w.deletedAt IS NULL GROUP BY w.currency")
        List<Object[]> getWalletCountByCurrency();

        @Query("SELECT w.currency, SUM(w.balance) FROM UserWallet w WHERE w.status = :status AND w.deletedAt IS NULL GROUP BY w.currency")
        List<Object[]> getTotalBalanceByCurrency(@Param("status") WalletStatus status);

        boolean existsByUserIdAndWalletTypeAndDeletedAtIsNull(Long userId, WalletType walletType);
        boolean existsByUserUuidAndWalletTypeAndDeletedAtIsNull(UUID userUuid, WalletType walletType);
        boolean existsByWalletAddressAndDeletedAtIsNull(String walletAddress);

        @Query("SELECT COUNT(w) FROM UserWallet w WHERE w.userId = :userId AND w.status = :status AND w.deletedAt IS NULL")
        long countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") WalletStatus status);

        @Modifying
        @Query("UPDATE UserWallet w SET w.deletedAt = :deletedAt WHERE w.userId = :userId")
        void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

        @Modifying
        @Query("UPDATE UserWallet w SET w.deletedAt = :deletedAt WHERE w.userUuid = :userUuid")
        void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);

        @Modifying
        @Query("UPDATE UserWallet w SET w.balance = w.balance + :amount, w.availableBalance = w.availableBalance + :amount WHERE w.id = :walletId")
        void creditWallet(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

        @Modifying
        @Query("UPDATE UserWallet w SET w.balance = w.balance - :amount, w.availableBalance = w.availableBalance - :amount WHERE w.id = :walletId")
        void debitWallet(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

        @Modifying
        @Query("UPDATE UserWallet w SET w.availableBalance = w.availableBalance - :amount, w.pendingBalance = w.pendingBalance + :amount WHERE w.id = :walletId")
        void freezeAmount(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

        @Modifying
        @Query("UPDATE UserWallet w SET w.availableBalance = w.availableBalance + :amount, w.pendingBalance = w.pendingBalance - :amount WHERE w.id = :walletId")
        void unfreezeAmount(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);
    }

    @Repository
    interface UserSecurityRepository extends JpaRepository<User.UserSecurity, Long> {

        Optional<User.UserSecurity> findByUserIdAndDeletedAtIsNull(Long userId);
        Optional<User.UserSecurity> findByUserUuidAndDeletedAtIsNull(UUID userUuid);

        boolean existsByUserIdAndDeletedAtIsNull(Long userId);
        boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);

        Optional<User.UserSecurity> findByPasswordResetTokenAndDeletedAtIsNull(String token);
        Optional<User.UserSecurity> findByEmailVerificationTokenAndDeletedAtIsNull(String token);
        Optional<User.UserSecurity> findByPhoneVerificationCodeAndDeletedAtIsNull(String code);

        List<User.UserSecurity> findByTwoFactorEnabledAndDeletedAtIsNull(boolean enabled);
        List<User.UserSecurity> findByBiometricEnabledAndDeletedAtIsNull(boolean enabled);
        List<User.UserSecurity> findByTransactionPinSetAndDeletedAtIsNull(boolean pinSet);

        @Query("SELECT s FROM UserSecurity s WHERE s.lockedUntil > :currentTime AND s.deletedAt IS NULL")
        List<User.UserSecurity> findLockedAccounts(@Param("currentTime") LocalDateTime currentTime);

        @Query("SELECT s FROM UserSecurity s WHERE s.pinLockedUntil > :currentTime AND s.deletedAt IS NULL")
        List<User.UserSecurity> findPinLockedAccounts(@Param("currentTime") LocalDateTime currentTime);

        @Query("SELECT s FROM UserSecurity s WHERE s.loginAttempts >= :maxAttempts AND s.deletedAt IS NULL")
        List<User.UserSecurity> findAccountsWithExcessiveLoginAttempts(@Param("maxAttempts") Integer maxAttempts);

        @Query("SELECT s FROM UserSecurity s WHERE s.lastLogin >= :date AND s.deletedAt IS NULL")
        List<User.UserSecurity> findRecentLogins(@Param("date") LocalDateTime date);

        @Query("SELECT COUNT(s) FROM UserSecurity s WHERE s.twoFactorEnabled = true AND s.deletedAt IS NULL")
        long countUsersWithTwoFactorEnabled();

        @Query("SELECT COUNT(s) FROM UserSecurity s WHERE s.biometricEnabled = true AND s.deletedAt IS NULL")
        long countUsersWithBiometricEnabled();

        @Modifying
        @Query("UPDATE UserSecurity s SET s.deletedAt = :deletedAt WHERE s.userId = :userId")
        void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

        @Modifying
        @Query("UPDATE UserSecurity s SET s.deletedAt = :deletedAt WHERE s.userUuid = :userUuid")
        void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);

        @Modifying
        @Query("UPDATE UserSecurity s SET s.loginAttempts = 0, s.lockedUntil = null WHERE s.userId = :userId")
        void resetLoginAttempts(@Param("userId") Long userId);

        @Modifying
        @Query("UPDATE UserSecurity s SET s.pinAttempts = 0, s.pinLockedUntil = null WHERE s.userId = :userId")
        void resetPinAttempts(@Param("userId") Long userId);
    }

    // ============================================================================
    // USER PREFERENCES REPOSITORY
    // ============================================================================

    @Repository
    interface UserPreferencesRepository extends JpaRepository<User.UserPreferences, Long> {

        Optional<User.UserPreferences> findByUserIdAndDeletedAtIsNull(Long userId);
        Optional<User.UserPreferences> findByUserUuidAndDeletedAtIsNull(UUID userUuid);

        boolean existsByUserIdAndDeletedAtIsNull(Long userId);
        boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);

        List<User.UserPreferences> findByLanguageAndDeletedAtIsNull(String language);
        List<User.UserPreferences> findByTimezoneAndDeletedAtIsNull(String timezone);
        List<User.UserPreferences> findByCurrencyAndDeletedAtIsNull(String currency);
        List<User.UserPreferences> findByThemeAndDeletedAtIsNull(String theme);

        List<User.UserPreferences> findByEmailNotificationsAndDeletedAtIsNull(boolean enabled);
        List<User.UserPreferences> findBySmsNotificationsAndDeletedAtIsNull(boolean enabled);
        List<User.UserPreferences> findByPushNotificationsAndDeletedAtIsNull(boolean enabled);

        @Query("SELECT COUNT(p) FROM UserPreferences p WHERE p.emailNotifications = true AND p.deletedAt IS NULL")
        long countUsersWithEmailNotificationsEnabled();

        @Query("SELECT COUNT(p) FROM UserPreferences p WHERE p.smsNotifications = true AND p.deletedAt IS NULL")
        long countUsersWithSmsNotificationsEnabled();

        @Query("SELECT COUNT(p) FROM UserPreferences p WHERE p.pushNotifications = true AND p.deletedAt IS NULL")
        long countUsersWithPushNotificationsEnabled();

        @Query("SELECT p.language, COUNT(p) FROM UserPreferences p WHERE p.deletedAt IS NULL GROUP BY p.language")
        List<Object[]> getLanguageDistribution();

        @Query("SELECT p.currency, COUNT(p) FROM UserPreferences p WHERE p.deletedAt IS NULL GROUP BY p.currency")
        List<Object[]> getCurrencyDistribution();

        @Modifying
        @Query("UPDATE UserPreferences p SET p.deletedAt = :deletedAt WHERE p.userId = :userId")
        void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

        @Modifying
        @Query("UPDATE UserPreferences p SET p.deletedAt = :deletedAt WHERE p.userUuid = :userUuid")
        void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);
    }
