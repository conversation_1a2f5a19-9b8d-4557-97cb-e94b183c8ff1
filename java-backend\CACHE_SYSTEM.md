# AeTrust Cache System Documentation

## Overview

The AeTrust backend implements a flexible caching system that automatically switches between Redis and in-memory storage based on availability. This ensures your application works seamlessly whether Redis is available or not.

## Architecture

### CacheService (Global Cache Manager)
- **Location**: `src/main/java/com/aetrust/services/CacheService.java`
- **Purpose**: Provides a unified interface for caching operations
- **Auto-Detection**: Automatically detects Redis availability and falls back to in-memory storage

### InMemorySessionService (Fallback Storage)
- **Location**: `src/main/java/com/aetrust/services/InMemorySessionService.java`
- **Purpose**: Thread-safe in-memory cache with TTL support
- **Features**: Automatic cleanup, expiration handling, concurrent access

### RedisConfig (Redis Configuration)
- **Location**: `src/main/java/com/aetrust/config/RedisConfig.java`
- **Purpose**: Configures Redis connection when enabled
- **Conditional**: Only loads when `spring.redis.enabled=true`

## Configuration

### Environment Variables

```bash
# Enable/Disable Redis
REDIS_ENABLED=false          # Set to true to enable Redis

# Redis Connection (when enabled)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=              # Optional
REDIS_DB=0

# Redis Pool Settings
REDIS_POOL_MAX_ACTIVE=10
REDIS_POOL_MAX_IDLE=10
REDIS_POOL_MIN_IDLE=1
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
```

### Application Configuration

```yaml
spring:
  redis:
    enabled: ${REDIS_ENABLED:false}
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DB:0}
```

## Usage Examples

### Basic Operations

```java
@Autowired
private CacheService cacheService;

// Store data with TTL
cacheService.set("user:123", "john_doe", 3600, TimeUnit.SECONDS);

// Retrieve data
String username = cacheService.get("user:123");

// Check existence
boolean exists = cacheService.exists("user:123");

// Delete data
cacheService.delete("user:123");
```

### Advanced Operations

```java
// Increment counter
Long visits = cacheService.increment("page_visits");

// Set expiration on existing key
cacheService.expire("session:abc", 1800, TimeUnit.SECONDS);

// Get cache information
Map<String, Object> info = cacheService.getCacheInfo();

// Health check
boolean healthy = cacheService.isHealthy();

// Clear all cache
cacheService.clear();
```

## Current Usage in Services

### AuthService
- **Refresh Tokens**: `refresh_token:{userId}`
- **Blacklisted Tokens**: `blacklisted_token:{tokenHash}`
- **Password Reset**: `password_reset:{token}`

### RegistrationService
- **Registration Sessions**: `registration:{registrationId}`
- **Phone Verification**: `verification:phone:{phone}`
- **Email Verification**: `verification:email:{email}`

### SecurityService
- **Brute Force Protection**: `bruteforce:{type}:{identifier}`
- **Account Lockouts**: `locked:{type}:{identifier}`

### SystemConfigService
- **Configuration Cache**: `config:{configKey}`

## Switching Between Redis and In-Memory

### To Enable Redis:
1. Set `REDIS_ENABLED=true` in environment
2. Ensure Redis server is running
3. Restart application
4. Cache operations automatically use Redis

### To Disable Redis:
1. Set `REDIS_ENABLED=false` in environment
2. Restart application
3. Cache operations automatically use in-memory storage

### Runtime Switching:
The system automatically detects Redis availability on each operation:
- If Redis is available → uses Redis
- If Redis fails → falls back to in-memory
- No code changes required

## Monitoring and Health Checks

### Cache Information Endpoint
```bash
GET /api/v1/cache/info
```

Response:
```json
{
  "success": true,
  "data": {
    "redis_enabled": false,
    "redis_available": false,
    "current_backend": "In-Memory",
    "total_keys": 42
  }
}
```

### Health Check Endpoint
```bash
GET /api/v1/cache/health
```

### Test Cache Operations
```bash
POST /api/v1/cache/test
Content-Type: application/json

{
  "key": "test_key",
  "value": "test_value",
  "ttl": 60
}
```

## Performance Considerations

### In-Memory Storage
- **Pros**: Fast, no network overhead, always available
- **Cons**: Limited by JVM memory, not shared across instances
- **Best For**: Development, single-instance deployments

### Redis Storage
- **Pros**: Shared across instances, persistent, scalable
- **Cons**: Network latency, requires Redis server
- **Best For**: Production, multi-instance deployments

## Testing

### Run Cache Tests
```bash
mvn test -Dtest=CacheServiceTest
```

### Test with Redis Disabled
```bash
mvn test -Dspring.redis.enabled=false
```

### Test with Redis Enabled
```bash
mvn test -Dspring.redis.enabled=true
```

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check Redis server is running
   - Verify connection settings
   - System automatically falls back to in-memory

2. **Memory Issues with In-Memory Cache**
   - Monitor JVM memory usage
   - Implement cache size limits if needed
   - Consider enabling Redis for production

3. **Cache Inconsistency**
   - Clear cache: `DELETE /api/v1/cache/clear`
   - Restart application
   - Check for concurrent access issues

### Logs to Monitor
```
INFO  - CacheService initialized - will auto-detect Redis availability
DEBUG - Cached in Redis: user:123
DEBUG - Cached in memory: user:123
WARN  - Redis not available: Connection refused
```

## Migration Guide

### From InMemorySessionService to CacheService

Old code:
```java
@Autowired
private InMemorySessionService sessionService;

sessionService.set(key, value, ttl, TimeUnit.SECONDS);
```

New code:
```java
@Autowired
private CacheService cacheService;

cacheService.set(key, value, ttl, TimeUnit.SECONDS);
```

All method signatures remain the same for seamless migration.

## Security Considerations

- Cache keys should not contain sensitive data
- Use appropriate TTL values for security tokens
- Monitor cache access patterns
- Implement proper cleanup for expired sessions
- Consider encryption for sensitive cached data

## Future Enhancements

- [ ] Cache size limits and eviction policies
- [ ] Distributed cache invalidation
- [ ] Cache warming strategies
- [ ] Metrics and monitoring integration
- [ ] Cache compression for large values
- [ ] Multi-level caching (L1: in-memory, L2: Redis)
