# Modular User Database Schema Design

## Overview
This document outlines the modular database schema design for user-related data, breaking down the monolithic user model into separate linked tables for better maintainability, scalability, and data organization.

## Core Design Principles
- **Separation of Concerns**: Each table handles a specific aspect of user data
- **Referential Integrity**: All tables linked via foreign keys to core users table
- **UUID Support**: Each user has both SQL ID and UUID for external references
- **Audit Trail**: All tables include created_at, updated_at timestamps
- **Soft Deletes**: Support for soft deletion across all tables
- **Indexing Strategy**: Proper indexing for performance optimization

## Table Structure

### 1. users (Core Table)
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    user_uuid UUID UNIQUE NOT NULL DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    username VARCHAR(50) UNIQUE,
    role VARCHAR(20) NOT NULL DEFAULT 'USER',
    account_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    kyc_status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    registration_step VARCHAR(30) NOT NULL DEFAULT 'PHONE_VERIFICATION',
    is_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    email_verified BOOLEAN DEFAULT FALSE,
    registration_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

### 2. user_profiles (Personal Information)
```sql
CREATE TABLE user_profiles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_uuid UUID NOT NULL REFERENCES users(user_uuid) ON DELETE CASCADE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    date_of_birth DATE,
    bio TEXT,
    profile_picture VARCHAR(500),
    street VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    UNIQUE(user_id),
    UNIQUE(user_uuid)
);
```

### 3. user_wallets (Financial Information)
```sql
CREATE TABLE user_wallets (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_uuid UUID NOT NULL REFERENCES users(user_uuid) ON DELETE CASCADE,
    wallet_type VARCHAR(20) NOT NULL DEFAULT 'MAIN',
    currency VARCHAR(10) NOT NULL DEFAULT 'USD',
    balance DECIMAL(15,2) DEFAULT 0.00,
    available_balance DECIMAL(15,2) DEFAULT 0.00,
    pending_balance DECIMAL(15,2) DEFAULT 0.00,
    wallet_address VARCHAR(255) UNIQUE,
    is_default BOOLEAN DEFAULT TRUE,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    daily_limit DECIMAL(15,2) DEFAULT 10000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 100000.00,
    transaction_limit DECIMAL(15,2) DEFAULT 5000.00,
    last_transaction_date TIMESTAMP,
    total_transactions INTEGER DEFAULT 0,
    total_credited DECIMAL(15,2) DEFAULT 0.00,
    total_debited DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

### 4. user_security (Security & Authentication)
```sql
CREATE TABLE user_security (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_uuid UUID NOT NULL REFERENCES users(user_uuid) ON DELETE CASCADE,
    transaction_pin_hash VARCHAR(255),
    transaction_pin_set BOOLEAN DEFAULT FALSE,
    biometric_enabled BOOLEAN DEFAULT FALSE,
    biometric_enrolled_at TIMESTAMP,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP,
    email_verification_token VARCHAR(255),
    email_verification_expires TIMESTAMP,
    phone_verification_code VARCHAR(10),
    phone_verification_expires TIMESTAMP,
    last_login TIMESTAMP,
    last_login_ip INET,
    last_login_platform VARCHAR(50),
    pin_attempts INTEGER DEFAULT 0,
    pin_locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    UNIQUE(user_id),
    UNIQUE(user_uuid)
);
```

### 5. user_preferences (Settings & Preferences)
```sql
CREATE TABLE user_preferences (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_uuid UUID NOT NULL REFERENCES users(user_uuid) ON DELETE CASCADE,
    language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'UTC',
    currency VARCHAR(10) DEFAULT 'USD',
    theme VARCHAR(20) DEFAULT 'light',
    email_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT TRUE,
    push_notifications BOOLEAN DEFAULT TRUE,
    marketing_emails BOOLEAN DEFAULT FALSE,
    security_alerts BOOLEAN DEFAULT TRUE,
    transaction_alerts BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    UNIQUE(user_id),
    UNIQUE(user_uuid)
);
```

### 6. user_identity_verification (KYC Information)
```sql
CREATE TABLE user_identity_verification (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_uuid UUID NOT NULL REFERENCES users(user_uuid) ON DELETE CASCADE,
    id_type VARCHAR(20),
    id_number VARCHAR(100),
    id_document_front VARCHAR(500),
    id_document_back VARCHAR(500),
    selfie_photo VARCHAR(500),
    verification_status VARCHAR(20) DEFAULT 'PENDING',
    verified_at TIMESTAMP,
    rejection_reason TEXT,
    verification_notes TEXT,
    verifier_id BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    UNIQUE(user_id),
    UNIQUE(user_uuid)
);
```

### 7. user_agent_info (Agent-specific Information)
```sql
CREATE TABLE user_agent_info (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_uuid UUID NOT NULL REFERENCES users(user_uuid) ON DELETE CASCADE,
    commission_rate DECIMAL(5,2) DEFAULT 0.00,
    total_transactions INTEGER DEFAULT 0,
    total_commission_earned DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    business_name VARCHAR(255),
    business_type VARCHAR(100),
    business_registration_number VARCHAR(100),
    business_address TEXT,
    business_document VARCHAR(500),
    tax_certificate VARCHAR(500),
    verification_status VARCHAR(20) DEFAULT 'PENDING',
    monthly_commission DECIMAL(15,2) DEFAULT 0.00,
    last_commission_date TIMESTAMP,
    performance_rating DECIMAL(3,2) DEFAULT 0.00,
    successful_transactions INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    UNIQUE(user_id),
    UNIQUE(user_uuid)
);
```

### 8. user_merchant_info (Merchant-specific Information)
```sql
CREATE TABLE user_merchant_info (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_uuid UUID NOT NULL REFERENCES users(user_uuid) ON DELETE CASCADE,
    business_name VARCHAR(255),
    business_type VARCHAR(100),
    business_registration VARCHAR(100),
    tax_id VARCHAR(100),
    merchant_category VARCHAR(100),
    website VARCHAR(255),
    business_description TEXT,
    verification_status VARCHAR(20) DEFAULT 'PENDING',
    api_key VARCHAR(255) UNIQUE,
    webhook_url VARCHAR(500),
    settlement_account VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    UNIQUE(user_id),
    UNIQUE(user_uuid)
);
```

## Indexes for Performance

```sql
-- Core users table indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_uuid ON users(user_uuid);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(account_status);
CREATE INDEX idx_users_kyc ON users(kyc_status);
CREATE INDEX idx_users_created ON users(created_at);
CREATE INDEX idx_users_deleted ON users(deleted_at);

-- User profiles indexes
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_uuid ON user_profiles(user_uuid);
CREATE INDEX idx_user_profiles_name ON user_profiles(first_name, last_name);

-- User wallets indexes
CREATE INDEX idx_user_wallets_user_id ON user_wallets(user_id);
CREATE INDEX idx_user_wallets_uuid ON user_wallets(user_uuid);
CREATE INDEX idx_user_wallets_type ON user_wallets(wallet_type);
CREATE INDEX idx_user_wallets_currency ON user_wallets(currency);
CREATE INDEX idx_user_wallets_status ON user_wallets(status);
CREATE INDEX idx_user_wallets_default ON user_wallets(is_default);

-- User security indexes
CREATE INDEX idx_user_security_user_id ON user_security(user_id);
CREATE INDEX idx_user_security_uuid ON user_security(user_uuid);
CREATE INDEX idx_user_security_reset_token ON user_security(password_reset_token);
CREATE INDEX idx_user_security_email_token ON user_security(email_verification_token);

-- User preferences indexes
CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX idx_user_preferences_uuid ON user_preferences(user_uuid);

-- Identity verification indexes
CREATE INDEX idx_identity_verification_user_id ON user_identity_verification(user_id);
CREATE INDEX idx_identity_verification_uuid ON user_identity_verification(user_uuid);
CREATE INDEX idx_identity_verification_status ON user_identity_verification(verification_status);

-- Agent info indexes
CREATE INDEX idx_agent_info_user_id ON user_agent_info(user_id);
CREATE INDEX idx_agent_info_uuid ON user_agent_info(user_uuid);
CREATE INDEX idx_agent_info_active ON user_agent_info(is_active);
CREATE INDEX idx_agent_info_verification ON user_agent_info(verification_status);

-- Merchant info indexes
CREATE INDEX idx_merchant_info_user_id ON user_merchant_info(user_id);
CREATE INDEX idx_merchant_info_uuid ON user_merchant_info(user_uuid);
CREATE INDEX idx_merchant_info_api_key ON user_merchant_info(api_key);
CREATE INDEX idx_merchant_info_verification ON user_merchant_info(verification_status);
```

## Benefits of This Design

1. **Scalability**: Each table can be optimized independently
2. **Maintainability**: Changes to one aspect don't affect others
3. **Performance**: Targeted queries and indexing
4. **Security**: Sensitive data isolated in specific tables
5. **Flexibility**: Easy to add new user-related features
6. **Data Integrity**: Foreign key constraints ensure consistency
7. **Audit Trail**: Complete tracking of changes across all tables

## Migration Strategy

1. Create new tables alongside existing user table
2. Migrate data from existing user table to new tables
3. Update application code to use new schema
4. Remove old user table columns after verification
5. Optimize indexes based on query patterns

## Usage Patterns

- **User Registration**: Insert into users, user_profiles, user_security, user_preferences
- **Profile Updates**: Update user_profiles table
- **Wallet Operations**: Query/update user_wallets table
- **Security Operations**: Query/update user_security table
- **KYC Process**: Update user_identity_verification table
- **Agent Operations**: Query/update user_agent_info table
