{"info": {"name": "AeTrust Backend API", "description": "Comprehensive API collection for AeTrust financial platform", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "agentId", "value": "", "type": "string"}, {"key": "transactionRef", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register - Initiate", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+************\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"password\": \"SecurePass123!\",\n  \"userType\": \"individual\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/initiate", "host": ["{{baseUrl}}"], "path": ["auth", "register", "initiate"]}}}, {"name": "Register - Verify Phone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+************\",\n  \"code\": \"123456\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/verify-phone", "host": ["{{baseUrl}}"], "path": ["auth", "register", "verify-phone"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.accessToken) {", "        pm.collectionVariables.set('authToken', response.data.accessToken);", "        console.log('Auth token saved:', response.data.accessToken);", "    }", "}"]}}]}, {"name": "Register - V<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"code\": \"123456\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/verify-email", "host": ["{{baseUrl}}"], "path": ["auth", "register", "verify-email"]}}}, {"name": "Register - Complete Personal Info", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"dateOfBirth\": \"1990-01-01\",\n  \"password\": \"SecurePass123!\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/personal-info", "host": ["{{baseUrl}}"], "path": ["auth", "register", "personal-info"]}}}, {"name": "Register - Set Transaction PIN", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"pin\": \"1234\",\n  \"confirmPin\": \"1234\",\n  \"platform\": \"web\",\n  \"phone\": \"+************\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/transaction-pin", "host": ["{{baseUrl}}"], "path": ["auth", "register", "transaction-pin"]}}}, {"name": "Register - Identity Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"idType\": \"national_id\",\n  \"idNumber\": \"**********123456\",\n  \"idDocumentFront\": \"base64_encoded_image\",\n  \"idDocumentBack\": \"base64_encoded_image\",\n  \"selfiePhoto\": \"base64_encoded_image\",\n  \"platform\": \"web\",\n  \"phone\": \"+************\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/identity-verification", "host": ["{{baseUrl}}"], "path": ["auth", "register", "identity-verification"]}}}, {"name": "Register - Biometric Enrollment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"biometricEnabled\": true,\n  \"platform\": \"web\",\n  \"phone\": \"+************\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/biometric-enrollment", "host": ["{{baseUrl}}"], "path": ["auth", "register", "biometric-enrollment"]}}}, {"name": "Register - Business Verification (Agents)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"businessName\": \"QuickCash Store\",\n  \"businessType\": \"sole_proprietorship\",\n  \"businessRegistrationNumber\": \"REG*********\",\n  \"businessAddress\": \"123 Business Street, Kigali, Rwanda\",\n  \"businessDocument\": \"base64_encoded_document\",\n  \"taxCertificate\": \"base64_encoded_certificate\",\n  \"phone\": \"+************\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/business-verification", "host": ["{{baseUrl}}"], "path": ["auth", "register", "business-verification"]}}}, {"name": "Register - Get Progress", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/auth/register/progress", "host": ["{{baseUrl}}"], "path": ["auth", "register", "progress"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.token) {", "        pm.collectionVariables.set('authToken', response.data.token);", "        pm.collectionVariables.set('userId', response.data.user.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}}]}, {"name": "User Management", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"bio\": \"Updated bio\",\n  \"address\": {\n    \"street\": \"456 New St\",\n    \"city\": \"Kigali\",\n    \"state\": \"Kigali\",\n    \"country\": \"Rwanda\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/users/profile", "host": ["{{baseUrl}}"], "path": ["users", "profile"]}}}, {"name": "Get User Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard", "host": ["{{baseUrl}}"], "path": ["users", "dashboard"]}}}, {"name": "Get Wallet Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard/wallet", "host": ["{{baseUrl}}"], "path": ["users", "dashboard", "wallet"]}}}]}, {"name": "Cash-In Operations", "item": [{"name": "Get Cash-In Options", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/cash-in/options?amount=100", "host": ["{{baseUrl}}"], "path": ["cash-in", "options"], "query": [{"key": "amount", "value": "100"}]}}}, {"name": "Calculate Cash-In Fees", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100,\n  \"currency\": \"USD\",\n  \"agentId\": \"{{agentId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/cash-in/calculate-fees", "host": ["{{baseUrl}}"], "path": ["cash-in", "calculate-fees"]}}}, {"name": "Initiate Cash-In", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.transactionRef) {", "        pm.collectionVariables.set('transactionRef', response.transactionRef);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100,\n  \"currency\": \"USD\",\n  \"agentId\": \"{{agentId}}\",\n  \"pin\": \"1234\",\n  \"description\": \"Cash deposit\"\n}"}, "url": {"raw": "{{baseUrl}}/cash-in/initiate", "host": ["{{baseUrl}}"], "path": ["cash-in", "initiate"]}}}, {"name": "Get Cash-In Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/cash-in/status/{{transactionRef}}", "host": ["{{baseUrl}}"], "path": ["cash-in", "status", "{{transactionRef}}"]}}}, {"name": "Get Cash-In History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/cash-in/history?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["cash-in", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}]}, {"name": "Transfers", "item": [{"name": "Send Money (P2P)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipientPhone\": \"+250788654321\",\n  \"amount\": 50,\n  \"currency\": \"USD\",\n  \"pin\": \"1234\",\n  \"description\": \"Payment for services\"\n}"}, "url": {"raw": "{{baseUrl}}/transfers/send", "host": ["{{baseUrl}}"], "path": ["transfers", "send"]}}}, {"name": "Send to Business", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"business_123\",\n  \"amount\": 75,\n  \"currency\": \"USD\",\n  \"pin\": \"1234\",\n  \"description\": \"Purchase payment\",\n  \"accountNumber\": \"ACC123456\"\n}"}, "url": {"raw": "{{baseUrl}}/transfers/business", "host": ["{{baseUrl}}"], "path": ["transfers", "business"]}}}, {"name": "International Remittance", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipientCountry\": \"KE\",\n  \"recipientName\": \"<PERSON>\",\n  \"recipientPhone\": \"+************\",\n  \"amount\": 100,\n  \"sourceCurrency\": \"USD\",\n  \"targetCurrency\": \"KES\",\n  \"deliveryMethod\": \"mobile_money\",\n  \"pin\": \"1234\",\n  \"description\": \"Family support\"\n}"}, "url": {"raw": "{{baseUrl}}/transfers/remittance", "host": ["{{baseUrl}}"], "path": ["transfers", "remittance"]}}}, {"name": "Pay Bill", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"billType\": \"electricity\",\n  \"provider\": \"EUCL\",\n  \"accountNumber\": \"*********\",\n  \"amount\": 25,\n  \"currency\": \"USD\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/transfers/bill-payment", "host": ["{{baseUrl}}"], "path": ["transfers", "bill-payment"]}}}, {"name": "Get Transfer History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/transfers/history?page=1&limit=20&type=all", "host": ["{{baseUrl}}"], "path": ["transfers", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "type", "value": "all"}]}}}, {"name": "Get Exchange Rates", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/transfers/exchange-rates?from=USD&to=KES", "host": ["{{baseUrl}}"], "path": ["transfers", "exchange-rates"], "query": [{"key": "from", "value": "USD"}, {"key": "to", "value": "KES"}]}}}, {"name": "Get Bill Providers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/transfers/bill-providers?country=RW&type=electricity", "host": ["{{baseUrl}}"], "path": ["transfers", "bill-providers"], "query": [{"key": "country", "value": "RW"}, {"key": "type", "value": "electricity"}]}}}]}, {"name": "Loans", "item": [{"name": "Apply for <PERSON>an", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"loanType\": \"personal\",\n  \"amount\": 1000,\n  \"currency\": \"USD\",\n  \"termMonths\": 12,\n  \"purpose\": \"Business expansion\",\n  \"monthlyIncome\": 2000,\n  \"employmentStatus\": \"employed\",\n  \"employerName\": \"Tech Corp\",\n  \"collateral\": {\n    \"type\": \"property\",\n    \"value\": 5000,\n    \"description\": \"House deed\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/loans/apply", "host": ["{{baseUrl}}"], "path": ["loans", "apply"]}}}, {"name": "Get My Loans", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/loans/my-loans?status=all&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["loans", "my-loans"], "query": [{"key": "status", "value": "all"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Loan Offers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/loans/offers?amount=1000&termMonths=12", "host": ["{{baseUrl}}"], "path": ["loans", "offers"], "query": [{"key": "amount", "value": "1000"}, {"key": "termMonths", "value": "12"}]}}}, {"name": "Get Loan Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/loans/{{loanId}}", "host": ["{{baseUrl}}"], "path": ["loans", "{{loanId}}"]}}}, {"name": "Make Loan Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100,\n  \"pin\": \"1234\",\n  \"paymentMethod\": \"wallet\"\n}"}, "url": {"raw": "{{baseUrl}}/loans/{{loanId}}/payment", "host": ["{{baseUrl}}"], "path": ["loans", "{{loanId}}", "payment"]}}}]}, {"name": "Cards", "item": [{"name": "Create Card", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cardType\": \"visa\",\n  \"cardLevel\": \"standard\",\n  \"pin\": \"1234\",\n  \"billingAddress\": {\n    \"street\": \"123 Main St\",\n    \"city\": \"Kigali\",\n    \"state\": \"Kigali\",\n    \"country\": \"Rwanda\",\n    \"postalCode\": \"00000\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/cards/create", "host": ["{{baseUrl}}"], "path": ["cards", "create"]}}}, {"name": "Get My Cards", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/cards/my-cards", "host": ["{{baseUrl}}"], "path": ["cards", "my-cards"]}}}, {"name": "Get Card Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/cards/{{cardId}}", "host": ["{{baseUrl}}"], "path": ["cards", "{{cardId}}"]}}}, {"name": "Update Card Limits", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"dailyLimit\": 1000,\n  \"monthlyLimit\": 10000,\n  \"singleTransactionLimit\": 500,\n  \"atmDailyLimit\": 300,\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/cards/{{cardId}}/limits", "host": ["{{baseUrl}}"], "path": ["cards", "{{cardId}}", "limits"]}}}, {"name": "Block/Unblock Card", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"block\",\n  \"reason\": \"Lost card\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/cards/{{cardId}}/block", "host": ["{{baseUrl}}"], "path": ["cards", "{{cardId}}", "block"]}}}]}, {"name": "Agents", "item": [{"name": "Register as Agent", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"businessName\": \"QuickCash Store\",\n  \"businessType\": \"retail\",\n  \"businessRegistrationNumber\": \"REG123456\",\n  \"location\": {\n    \"address\": \"123 Business St, Kigali\",\n    \"coordinates\": {\n      \"latitude\": -1.9441,\n      \"longitude\": 30.0619\n    }\n  },\n  \"contactInfo\": {\n    \"phone\": \"+250788999888\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"operatingHours\": {\n    \"start\": 8,\n    \"end\": 20\n  }\n}"}, "url": {"raw": "{{baseUrl}}/agents/register", "host": ["{{baseUrl}}"], "path": ["agents", "register"]}}}, {"name": "Get Agent Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/agents/profile", "host": ["{{baseUrl}}"], "path": ["agents", "profile"]}}}, {"name": "Search Agents", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/agents/search?lat=-1.9441&lon=30.0619&radius=5&service=cash_in", "host": ["{{baseUrl}}"], "path": ["agents", "search"], "query": [{"key": "lat", "value": "-1.9441"}, {"key": "lon", "value": "30.0619"}, {"key": "radius", "value": "5"}, {"key": "service", "value": "cash_in"}]}}}, {"name": "Get Agent by Code", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/agents/code/AGT001", "host": ["{{baseUrl}}"], "path": ["agents", "code", "AGT001"]}}}, {"name": "Agent Cash-In Operation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customerPhone\": \"+************\",\n  \"amount\": 100,\n  \"currency\": \"USD\",\n  \"agentPin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/agents/cash-in", "host": ["{{baseUrl}}"], "path": ["agents", "cash-in"]}}}, {"name": "Agent Cash-Out Operation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customerPhone\": \"+************\",\n  \"amount\": 50,\n  \"currency\": \"USD\",\n  \"agentPin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/agents/cash-out", "host": ["{{baseUrl}}"], "path": ["agents", "cash-out"]}}}]}, {"name": "Savings", "item": [{"name": "Create Savings Account", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"accountType\": \"fixed_deposit\",\n  \"currency\": \"USD\",\n  \"initialDeposit\": 100,\n  \"termMonths\": 12,\n  \"autoRenewal\": true,\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/savings/create", "host": ["{{baseUrl}}"], "path": ["savings", "create"]}}}, {"name": "Get My Savings Accounts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/savings/my-accounts", "host": ["{{baseUrl}}"], "path": ["savings", "my-accounts"]}}}, {"name": "Deposit to Savings", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 50,\n  \"pin\": \"1234\",\n  \"description\": \"Monthly savings\"\n}"}, "url": {"raw": "{{baseUrl}}/savings/{{savingsId}}/deposit", "host": ["{{baseUrl}}"], "path": ["savings", "{{savingsId}}", "deposit"]}}}, {"name": "Withdraw from Savings", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 25,\n  \"pin\": \"1234\",\n  \"reason\": \"Emergency withdrawal\"\n}"}, "url": {"raw": "{{baseUrl}}/savings/{{savingsId}}/withdraw", "host": ["{{baseUrl}}"], "path": ["savings", "{{savingsId}}", "withdraw"]}}}]}, {"name": "Top-up", "item": [{"name": "Bank Transfer Top-up", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100,\n  \"currency\": \"USD\",\n  \"bankCode\": \"BNR001\",\n  \"accountNumber\": \"**********\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/topup/bank-transfer", "host": ["{{baseUrl}}"], "path": ["topup", "bank-transfer"]}}}, {"name": "Card Payment Top-up", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 50,\n  \"currency\": \"USD\",\n  \"cardNumber\": \"****************\",\n  \"expiryMonth\": \"12\",\n  \"expiryYear\": \"25\",\n  \"cvv\": \"123\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/topup/card-payment", "host": ["{{baseUrl}}"], "path": ["topup", "card-payment"]}}}, {"name": "Agent <PERSON><PERSON><PERSON><PERSON> Top-up", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 75,\n  \"currency\": \"USD\",\n  \"agentCode\": \"AGT001\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/topup/agent-deposit", "host": ["{{baseUrl}}"], "path": ["topup", "agent-deposit"]}}}, {"name": "Find Nearest Agents", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/topup/agents/nearest?lat=-1.9441&lon=30.0619&radius=10", "host": ["{{baseUrl}}"], "path": ["topup", "agents", "nearest"], "query": [{"key": "lat", "value": "-1.9441"}, {"key": "lon", "value": "30.0619"}, {"key": "radius", "value": "10"}]}}}, {"name": "Get Top-up History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/topup/history?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["topup", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Top-up Methods", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/topup/methods", "host": ["{{baseUrl}}"], "path": ["topup", "methods"]}}}, {"name": "Get Supported Banks", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/topup/banks?country=RW", "host": ["{{baseUrl}}"], "path": ["topup", "banks"], "query": [{"key": "country", "value": "RW"}]}}}]}, {"name": "Notifications", "item": [{"name": "Get Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/notifications?page=1&limit=20&read=false&category=transaction", "host": ["{{baseUrl}}"], "path": ["notifications"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "read", "value": "false"}, {"key": "category", "value": "transaction"}]}}}, {"name": "Get Notification History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/notifications/history?days=30", "host": ["{{baseUrl}}"], "path": ["notifications", "history"], "query": [{"key": "days", "value": "30"}]}}}, {"name": "Get Unread Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/notifications/unread-count", "host": ["{{baseUrl}}"], "path": ["notifications", "unread-count"]}}}, {"name": "<PERSON> <PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/notifications/{{notificationId}}/read", "host": ["{{baseUrl}}"], "path": ["notifications", "{{notificationId}}", "read"]}}}, {"name": "<PERSON> as <PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/notifications/mark-all-read", "host": ["{{baseUrl}}"], "path": ["notifications", "mark-all-read"]}}}, {"name": "Delete Notification", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/notifications/{{notificationId}}", "host": ["{{baseUrl}}"], "path": ["notifications", "{{notificationId}}"]}}}, {"name": "Update Notification Preferences", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": true,\n  \"sms\": true,\n  \"push\": false,\n  \"categories\": {\n    \"transaction\": true,\n    \"security\": true,\n    \"marketing\": false,\n    \"system\": true\n  }\n}"}, "url": {"raw": "{{baseUrl}}/notifications/preferences", "host": ["{{baseUrl}}"], "path": ["notifications", "preferences"]}}}]}, {"name": "Admin Portal", "item": [{"name": "Get Dashboard Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/dashboard", "host": ["{{baseUrl}}"], "path": ["admin", "dashboard"]}}}, {"name": "Get System Health", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/health", "host": ["{{baseUrl}}"], "path": ["admin", "health"]}}}, {"name": "Get Audit Logs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/audit-logs?page=1&limit=50&action=login&userId={{userId}}", "host": ["{{baseUrl}}"], "path": ["admin", "audit-logs"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "action", "value": "login"}, {"key": "userId", "value": "{{userId}}"}]}}}, {"name": "<PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/fraud-alerts?status=pending&severity=high", "host": ["{{baseUrl}}"], "path": ["admin", "fraud-alerts"], "query": [{"key": "status", "value": "pending"}, {"key": "severity", "value": "high"}]}}}, {"name": "Get Reports", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/reports?type=transaction&period=monthly&format=json", "host": ["{{baseUrl}}"], "path": ["admin", "reports"], "query": [{"key": "type", "value": "transaction"}, {"key": "period", "value": "monthly"}, {"key": "format", "value": "json"}]}}}, {"name": "Get Pending KYC", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/kyc/pending?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["admin", "kyc", "pending"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Approve KYC", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"comments\": \"All documents verified successfully\",\n  \"verificationLevel\": \"full\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/kyc/{{userId}}/approve", "host": ["{{baseUrl}}"], "path": ["admin", "kyc", "{{userId}}", "approve"]}}}, {"name": "Reject KYC", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Invalid document provided\",\n  \"comments\": \"Please resubmit with valid ID document\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/kyc/{{userId}}/reject", "host": ["{{baseUrl}}"], "path": ["admin", "kyc", "{{userId}}", "reject"]}}}, {"name": "Get Security Config", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/security/config", "host": ["{{baseUrl}}"], "path": ["admin", "security", "config"]}}}, {"name": "Update Security Config", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"maxLoginAttempts\": 5,\n  \"lockoutDuration\": 1800,\n  \"passwordMinLength\": 8,\n  \"requireTwoFactor\": true,\n  \"sessionTimeout\": 3600\n}"}, "url": {"raw": "{{baseUrl}}/admin/security/config", "host": ["{{baseUrl}}"], "path": ["admin", "security", "config"]}}}, {"name": "Get Advanced Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/analytics/advanced?period=7d&metrics=transactions,users,revenue", "host": ["{{baseUrl}}"], "path": ["admin", "analytics", "advanced"], "query": [{"key": "period", "value": "7d"}, {"key": "metrics", "value": "transactions,users,revenue"}]}}}, {"name": "Get Merchants", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/merchants?status=active&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["admin", "merchants"], "query": [{"key": "status", "value": "active"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Credit Cards (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/credit-cards?status=active&userId={{userId}}&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["admin", "credit-cards"], "query": [{"key": "status", "value": "active"}, {"key": "userId", "value": "{{userId}}"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Loans (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/admin/loans?status=active&loanType=personal&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["admin", "loans"], "query": [{"key": "status", "value": "active"}, {"key": "loanType", "value": "personal"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}]}, {"name": "Health & Monitoring", "item": [{"name": "API Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "System Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/metrics", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["metrics"]}}}, {"name": "Auth Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/health", "host": ["{{baseUrl}}"], "path": ["auth", "health"]}}}, {"name": "User Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/health", "host": ["{{baseUrl}}"], "path": ["users", "health"]}}}, {"name": "Transfer Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/transfers/health", "host": ["{{baseUrl}}"], "path": ["transfers", "health"]}}}, {"name": "Cash-In Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cash-in/health", "host": ["{{baseUrl}}"], "path": ["cash-in", "health"]}}}, {"name": "Admin Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/admin/service-health", "host": ["{{baseUrl}}"], "path": ["admin", "service-health"]}}}]}]}