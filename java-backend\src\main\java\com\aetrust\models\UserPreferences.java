package com.aetrust.models;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_preferences", indexes = {
    @Index(name = "idx_user_preferences_user_id", columnList = "userId"),
    @Index(name = "idx_user_preferences_uuid", columnList = "userUuid")
})
@EntityListeners(AuditingEntityListener.class)
public class UserPreferences {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false, unique = true)
    private Long userId;

    @Column(name = "user_uuid", nullable = false, unique = true)
    private UUID userUuid;

    @Column(length = 10)
    private String language = "en";

    @Column(length = 50)
    private String timezone = "UTC";

    @Column(length = 10)
    private String currency = "USD";

    @Column(length = 20)
    private String theme = "light";

    @Column(name = "email_notifications")
    private boolean emailNotifications = true;

    @Column(name = "sms_notifications")
    private boolean smsNotifications = true;

    @Column(name = "push_notifications")
    private boolean pushNotifications = true;

    @Column(name = "marketing_emails")
    private boolean marketingEmails = false;

    @Column(name = "security_alerts")
    private boolean securityAlerts = true;

    @Column(name = "transaction_alerts")
    private boolean transactionAlerts = true;

    @CreatedDate
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    // helper methods
    public boolean isNotificationEnabled(String type) {
        switch (type.toLowerCase()) {
            case "email":
                return emailNotifications;
            case "sms":
                return smsNotifications;
            case "push":
                return pushNotifications;
            case "marketing":
                return marketingEmails;
            case "security":
                return securityAlerts;
            case "transaction":
                return transactionAlerts;
            default:
                return false;
        }
    }

    public void enableAllNotifications() {
        emailNotifications = true;
        smsNotifications = true;
        pushNotifications = true;
        securityAlerts = true;
        transactionAlerts = true;
    }

    public void disableAllNotifications() {
        emailNotifications = false;
        smsNotifications = false;
        pushNotifications = false;
        marketingEmails = false;
        securityAlerts = false;
        transactionAlerts = false;
    }
}
