package com.aetrust.controllers;

import com.aetrust.services.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Slf4j
@RestController
@RequestMapping("/cache")
public class CacheController {

    @Autowired
    private CacheService cacheService;

 
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getCacheInfo() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Cache info retrieved");
            response.put("data", cacheService.getCacheInfo());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting cache info: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to get cache info");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

  
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        try {
            boolean healthy = cacheService.isHealthy();
            Map<String, Object> response = new HashMap<>();
            response.put("success", healthy);
            response.put("message", healthy ? "Cache is healthy" : "Cache health check failed");
            response.put("data", Map.of("healthy", healthy));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Cache health check error: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Cache health check failed");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

  
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testCache(@RequestBody Map<String, String> request) {
        try {
            String key = request.getOrDefault("key", "test_key");
            String value = request.getOrDefault("value", "test_value");
            int ttl = Integer.parseInt(request.getOrDefault("ttl", "60"));

            cacheService.set(key, value, ttl, TimeUnit.SECONDS);
            String retrieved = cacheService.get(key);
            boolean exists = cacheService.exists(key);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Cache test completed");
            response.put("data", Map.of(
                "key", key,
                "value_set", value,
                "value_retrieved", retrieved,
                "exists", exists,
                "test_passed", value.equals(retrieved)
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Cache test error: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Cache test failed");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

 
    @DeleteMapping("/clear")
    public ResponseEntity<Map<String, Object>> clearCache() {
        try {
            cacheService.clear();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Cache cleared successfully");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Cache clear error: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to clear cache");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

 
    @GetMapping("/get/{key}")
    public ResponseEntity<Map<String, Object>> getCacheValue(@PathVariable String key) {
        try {
            String value = cacheService.get(key);
            boolean exists = cacheService.exists(key);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", exists ? "Cache value found" : "Cache key not found");
            response.put("data", Map.of(
                "key", key,
                "value", value,
                "exists", exists
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting cache value for key {}: {}", key, e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to get cache value");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

  
    @PostMapping("/set")
    public ResponseEntity<Map<String, Object>> setCacheValue(@RequestBody Map<String, Object> request) {
        try {
            String key = (String) request.get("key");
            String value = (String) request.get("value");
            Integer ttl = (Integer) request.getOrDefault("ttl", 3600);

            if (key == null || value == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Key and value are required");
                return ResponseEntity.badRequest().body(response);
            }

            cacheService.set(key, value, ttl, TimeUnit.SECONDS);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Cache value set successfully");
            response.put("data", Map.of(
                "key", key,
                "value", value,
                "ttl_seconds", ttl
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error setting cache value: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to set cache value");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

   
    @DeleteMapping("/delete/{key}")
    public ResponseEntity<Map<String, Object>> deleteCacheKey(@PathVariable String key) {
        try {
            boolean existed = cacheService.exists(key);
            cacheService.delete(key);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", existed ? "Cache key deleted" : "Cache key did not exist");
            response.put("data", Map.of(
                "key", key,
                "existed", existed
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error deleting cache key {}: {}", key, e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to delete cache key");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }
}
