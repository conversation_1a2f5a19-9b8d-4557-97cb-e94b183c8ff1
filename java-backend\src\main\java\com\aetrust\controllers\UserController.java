package com.aetrust.controllers;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.dto.ApiResponse;
import com.aetrust.services.UserService;
import com.aetrust.services.SecurityService;
import com.aetrust.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/users")
@Validated
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SecurityService securityService;
    
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCurrentUser(
            Authentication authentication,
            HttpServletRequest request) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            if (userPayload == null || userPayload.getId() == null) {
                return ResponseEntity.status(401).body(
                    ApiResponse.error("user not authenticated", "NOT_AUTHENTICATED"));
            }

            UserService.CompleteUserResult result = userService.getCompleteUserById(userPayload.getId());

            if (!result.isSuccess()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("user not found", "USER_NOT_FOUND"));
            }

            return ResponseEntity.ok(
                ApiResponse.success("user profile retrieved successfully", result.getData()));

        } catch (Exception error) {
            log.error("Error fetching user profile: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to fetch user profile", "INTERNAL_ERROR"));
        }
    }
    
    @PutMapping("/profile")
    public ResponseEntity<ApiResponse<Map<String, Object>>> updateProfile(
            @Valid @RequestBody UpdateProfileRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            String ipAddress = getClientIp(httpRequest);
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                userPayload.getId(), "user", "/users/profile");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            // use the new modular profile update method
            UserService.ProfileUpdateResult result = userService.updateUserProfile(userPayload.getId(), request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("profileUpdated", true);
            responseData.put("updatedFields", result.getData());
            
            return ResponseEntity.ok(
                ApiResponse.success("profile updated successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error updating profile: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("profile update failed", "INTERNAL_ERROR"));
        }
    }
    
    @PutMapping("/password")
    public ResponseEntity<ApiResponse<Map<String, Object>>> updatePassword(
            @Valid @RequestBody UpdatePasswordRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            String ipAddress = getClientIp(httpRequest);
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                userPayload.getId(), "user", "/users/password");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            UserService.PasswordUpdateResult result = userService.updatePassword(
                userPayload.getId(), request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("passwordUpdated", true);
            responseData.put("requiresReauth", true);
            
            return ResponseEntity.ok(
                ApiResponse.success("password updated successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error updating password: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("password update failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/profile-picture")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadProfilePicture(
            @Valid @RequestBody UploadProfilePictureRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            
            UserService.UploadResult result = userService.uploadProfilePicture(
                userPayload.getId(), request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("profilePictureUploaded", true);
            responseData.put("profilePictureUrl", result.getFileUrl());
            
            return ResponseEntity.ok(
                ApiResponse.success("profile picture uploaded successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error uploading profile picture: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("profile picture upload failed", "INTERNAL_ERROR"));
        }
    }
    
    @DeleteMapping("/account")
    public ResponseEntity<ApiResponse<Map<String, Object>>> deleteAccount(
            @Valid @RequestBody DeleteAccountRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            String ipAddress = getClientIp(httpRequest);
            
            SecurityService.ActivityContext activity = new SecurityService.ActivityContext(
                ipAddress, httpRequest.getHeader("User-Agent"), System.currentTimeMillis());
            
            SecurityService.SuspiciousActivityResult suspiciousResult = 
                securityService.detectSuspiciousActivity(userPayload.getId(), activity);
            
            if (suspiciousResult.getRiskScore() > 30) {
                return ResponseEntity.status(403).body(
                    ApiResponse.error("account deletion blocked due to suspicious activity", 
                        "SUSPICIOUS_ACTIVITY"));
            }
            
            UserService.DeletionResult result = userService.deleteAccount(
                userPayload.getId(), request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("accountDeleted", true);
            responseData.put("deletionScheduled", result.isDeletionScheduled());
            responseData.put("finalDeletionDate", result.getFinalDeletionDate());
            
            return ResponseEntity.ok(
                ApiResponse.success("account deletion initiated", responseData));
                
        } catch (Exception error) {
            log.error("Error deleting account: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("account deletion failed", "INTERNAL_ERROR"));
        }
    }
    
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Map<String, Object>>> searchUsers(
            @RequestParam String query,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int limit,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                userPayload.getId(), "user", "/users/search");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("search rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            UserService.SearchResult result = userService.searchUsers(query, page, limit);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("users", result.getUsers());
            responseData.put("totalCount", result.getTotalCount());
            responseData.put("currentPage", page);
            responseData.put("totalPages", result.getTotalPages());
            responseData.put("hasMore", result.isHasMore());
            
            return ResponseEntity.ok(
                ApiResponse.success("user search completed", responseData));
                
        } catch (Exception error) {
            log.error("Error searching users: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("user search failed", "INTERNAL_ERROR"));
        }
    }
    
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserStats(
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            UserService.CompleteUserResult result = userService.getCompleteUserById(userPayload.getId());

            if (!result.isSuccess()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("user not found", "USER_NOT_FOUND"));
            }

            Map<String, Object> userData = (Map<String, Object>) result.getData();
            Map<String, Object> statsData = new HashMap<>();

            statsData.put("totalTransactions", 0);
            statsData.put("totalBalance", "0.00");
            statsData.put("accountAge", calculateAccountAge(userData));
            statsData.put("verificationLevel", calculateVerificationLevel(userData));

            return ResponseEntity.ok(
                ApiResponse.success("user stats retrieved successfully", statsData));

        } catch (Exception error) {
            log.error("Error fetching user stats: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to fetch user stats", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/verify/{token}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> verifyEmail(
            @PathVariable String token,
            HttpServletRequest httpRequest) {

        try {
            // TODO implement email verification logic
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("emailVerified", true);
            responseData.put("message", "email verification successful");

            return ResponseEntity.ok(
                ApiResponse.success("email verified successfully", responseData));

        } catch (Exception error) {
            log.error("Error verifying email: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("email verification failed", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/dashboard")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserDashboard(
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            UserService.CompleteUserResult result = userService.getCompleteUserById(userPayload.getId());

            if (!result.isSuccess()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("user not found", "USER_NOT_FOUND"));
            }

            Map<String, Object> userData = (Map<String, Object>) result.getData();
            Map<String, Object> dashboardData = createDashboardData(userData);

            return ResponseEntity.ok(
                ApiResponse.success("dashboard data retrieved successfully", dashboardData));

        } catch (Exception error) {
            log.error("Error fetching dashboard data: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to fetch dashboard data", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/dashboard/enhanced")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEnhancedDashboard(
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            UserService.CompleteUserResult result = userService.getCompleteUserById(userPayload.getId());

            if (!result.isSuccess()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("user not found", "USER_NOT_FOUND"));
            }

            Map<String, Object> userData = (Map<String, Object>) result.getData();
            Map<String, Object> enhancedData = createEnhancedDashboardData(userData);

            return ResponseEntity.ok(
                ApiResponse.success("enhanced dashboard data retrieved successfully", enhancedData));

        } catch (Exception error) {
            log.error("Error fetching enhanced dashboard data: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to fetch enhanced dashboard data", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/dashboard/profile")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserProfile(
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            UserService.CompleteUserResult result = userService.getCompleteUserById(userPayload.getId());

            if (!result.isSuccess()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("user not found", "USER_NOT_FOUND"));
            }

            Map<String, Object> userData = (Map<String, Object>) result.getData();
            Map<String, Object> profileData = extractProfileData(userData);

            return ResponseEntity.ok(
                ApiResponse.success("user profile data retrieved successfully", profileData));

        } catch (Exception error) {
            log.error("Error fetching user profile data: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to fetch user profile data", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/dashboard/wallet")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getWalletSummary(
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            UserService.CompleteUserResult result = userService.getCompleteUserById(userPayload.getId());

            if (!result.isSuccess()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("user not found", "USER_NOT_FOUND"));
            }

            Map<String, Object> userData = (Map<String, Object>) result.getData();
            Map<String, Object> walletData = extractWalletData(userData);

            return ResponseEntity.ok(
                ApiResponse.success("wallet summary retrieved successfully", walletData));

        } catch (Exception error) {
            log.error("Error fetching wallet summary: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to fetch wallet summary", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/dashboard/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAccountStatus(
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            UserService.CompleteUserResult result = userService.getCompleteUserById(userPayload.getId());

            if (!result.isSuccess()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("user not found", "USER_NOT_FOUND"));
            }

            Map<String, Object> userData = (Map<String, Object>) result.getData();
            Map<String, Object> statusData = extractAccountStatus(userData);

            return ResponseEntity.ok(
                ApiResponse.success("account status retrieved successfully", statusData));

        } catch (Exception error) {
            log.error("Error fetching account status: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to fetch account status", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("status", "ok");
        healthData.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(
            ApiResponse.success("user service is running", healthData));
    }
    
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    private int calculateAccountAge(Map<String, Object> userData) {
        // calculate account age in days
        return 30; // placeholder
    }

    private String calculateVerificationLevel(Map<String, Object> userData) {
        Map<String, Object> user = (Map<String, Object>) userData.get("user");
        Map<String, Object> identity = (Map<String, Object>) userData.get("identity");

        if (identity != null && Boolean.TRUE.equals(identity.get("isVerified"))) {
            return "VERIFIED";
        } else if (Boolean.TRUE.equals(user.get("isVerified"))) {
            return "BASIC";
        }
        return "UNVERIFIED";
    }

    private Map<String, Object> createDashboardData(Map<String, Object> userData) {
        Map<String, Object> dashboard = new HashMap<>();
        Map<String, Object> user = (Map<String, Object>) userData.get("user");
        Map<String, Object> profile = (Map<String, Object>) userData.get("profile");

        dashboard.put("user", user);
        dashboard.put("profile", profile);
        dashboard.put("quickStats", createQuickStats(userData));
        dashboard.put("recentActivity", createRecentActivity());

        return dashboard;
    }

    private Map<String, Object> createEnhancedDashboardData(Map<String, Object> userData) {
        Map<String, Object> enhanced = createDashboardData(userData);
        enhanced.put("analytics", createAnalytics(userData));
        enhanced.put("recommendations", createRecommendations());
        enhanced.put("notifications", createNotifications());

        return enhanced;
    }

    private Map<String, Object> extractProfileData(Map<String, Object> userData) {
        Map<String, Object> profileData = new HashMap<>();
        profileData.put("user", userData.get("user"));
        profileData.put("profile", userData.get("profile"));
        profileData.put("preferences", userData.get("preferences"));

        return profileData;
    }

    private Map<String, Object> extractWalletData(Map<String, Object> userData) {
        Map<String, Object> walletData = new HashMap<>();
        walletData.put("wallets", userData.get("wallets"));
        walletData.put("summary", createWalletSummary(userData));

        return walletData;
    }

    private Map<String, Object> extractAccountStatus(Map<String, Object> userData) {
        Map<String, Object> user = (Map<String, Object>) userData.get("user");
        Map<String, Object> security = (Map<String, Object>) userData.get("security");
        Map<String, Object> identity = (Map<String, Object>) userData.get("identity");

        Map<String, Object> status = new HashMap<>();
        status.put("accountStatus", user.get("accountStatus"));
        status.put("kycStatus", user.get("kycStatus"));
        status.put("verificationLevel", calculateVerificationLevel(userData));
        status.put("securityLevel", calculateSecurityLevel(security));

        return status;
    }

    private Map<String, Object> createQuickStats(Map<String, Object> userData) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalBalance", "0.00");
        stats.put("totalTransactions", 0);
        stats.put("pendingTransactions", 0);
        stats.put("accountAge", calculateAccountAge(userData));

        return stats;
    }

    private Map<String, Object> createRecentActivity() {
        Map<String, Object> activity = new HashMap<>();
        activity.put("transactions", new ArrayList<>());
        activity.put("logins", new ArrayList<>());
        activity.put("updates", new ArrayList<>());

        return activity;
    }

    private Map<String, Object> createAnalytics(Map<String, Object> userData) {
        Map<String, Object> analytics = new HashMap<>();
        analytics.put("spendingPattern", new HashMap<>());
        analytics.put("transactionTrends", new HashMap<>());
        analytics.put("monthlyStats", new HashMap<>());

        return analytics;
    }

    private Map<String, Object> createRecommendations() {
        Map<String, Object> recommendations = new HashMap<>();
        recommendations.put("security", new ArrayList<>());
        recommendations.put("features", new ArrayList<>());
        recommendations.put("offers", new ArrayList<>());

        return recommendations;
    }

    private Map<String, Object> createNotifications() {
        Map<String, Object> notifications = new HashMap<>();
        notifications.put("unread", 0);
        notifications.put("recent", new ArrayList<>());
        notifications.put("important", new ArrayList<>());

        return notifications;
    }

    private Map<String, Object> createWalletSummary(Map<String, Object> userData) {
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalBalance", "0.00");
        summary.put("availableBalance", "0.00");
        summary.put("pendingBalance", "0.00");
        summary.put("walletCount", 1);

        return summary;
    }

    private String calculateSecurityLevel(Map<String, Object> security) {
        if (security == null) return "LOW";

        int score = 0;
        if (Boolean.TRUE.equals(security.get("twoFactorEnabled"))) score += 30;
        if (Boolean.TRUE.equals(security.get("biometricEnabled"))) score += 25;
        if (Boolean.TRUE.equals(security.get("transactionPinSet"))) score += 20;

        if (score >= 70) return "HIGH";
        if (score >= 40) return "MEDIUM";
        return "LOW";
    }
}
