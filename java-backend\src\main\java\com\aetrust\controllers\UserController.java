package com.aetrust.controllers;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.dto.ApiResponse;
import com.aetrust.services.UserService;
import com.aetrust.services.SecurityService;
import com.aetrust.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/users")
@Validated
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SecurityService securityService;
    
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCurrentUser(
            Authentication authentication,
            HttpServletRequest request) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            
            if (userPayload == null || userPayload.getId() == null) {
                return ResponseEntity.status(401).body(
                    ApiResponse.error("user not authenticated", "NOT_AUTHENTICATED"));
            }
            
            UserService.UserResult result = userService.getUserById(userPayload.getId());
            
            if (!result.isSuccess()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("user not found", "USER_NOT_FOUND"));
            }
            
            Map<String, Object> userData = new HashMap<>();
            userData.put("id", result.getUser().get("id"));
            userData.put("email", result.getUser().get("email"));
            userData.put("phone", result.getUser().get("phone"));
            userData.put("username", result.getUser().get("username"));
            userData.put("firstName", result.getUser().get("firstName"));
            userData.put("lastName", result.getUser().get("lastName"));
            userData.put("profilePicture", result.getUser().get("profilePicture"));
            userData.put("bio", result.getUser().get("bio"));
            userData.put("address", result.getUser().get("address"));
            userData.put("role", result.getUser().get("role"));
            userData.put("isVerified", result.getUser().get("isVerified"));
            userData.put("kycStatus", result.getUser().get("kycStatus"));
            userData.put("walletBalance", result.getUser().get("walletBalance"));
            userData.put("agentInfo", result.getUser().get("agentInfo"));
            userData.put("createdAt", result.getUser().get("createdAt"));
            
            return ResponseEntity.ok(
                ApiResponse.success("user profile retrieved successfully", userData));
                
        } catch (Exception error) {
            log.error("Error fetching user profile: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to fetch user profile", "INTERNAL_ERROR"));
        }
    }
    
    @PutMapping("/profile")
    public ResponseEntity<ApiResponse<Map<String, Object>>> updateProfile(
            @Valid @RequestBody UpdateProfileRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            String ipAddress = getClientIp(httpRequest);
            
            // rate limiting
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                userPayload.getId(), "user", "/users/profile");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            UserService.UpdateResult result = userService.updateProfile(userPayload.getId(), request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("profileUpdated", true);
            responseData.put("updatedFields", result.getUpdatedFields());
            
            return ResponseEntity.ok(
                ApiResponse.success("profile updated successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error updating profile: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("profile update failed", "INTERNAL_ERROR"));
        }
    }
    
    @PutMapping("/password")
    public ResponseEntity<ApiResponse<Map<String, Object>>> updatePassword(
            @Valid @RequestBody UpdatePasswordRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            String ipAddress = getClientIp(httpRequest);
            
            // rate limiting
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                userPayload.getId(), "user", "/users/password");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            UserService.PasswordUpdateResult result = userService.updatePassword(
                userPayload.getId(), request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("passwordUpdated", true);
            responseData.put("requiresReauth", true);
            
            return ResponseEntity.ok(
                ApiResponse.success("password updated successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error updating password: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("password update failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/profile-picture")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadProfilePicture(
            @Valid @RequestBody UploadProfilePictureRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            
            UserService.UploadResult result = userService.uploadProfilePicture(
                userPayload.getId(), request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("profilePictureUploaded", true);
            responseData.put("profilePictureUrl", result.getFileUrl());
            
            return ResponseEntity.ok(
                ApiResponse.success("profile picture uploaded successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error uploading profile picture: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("profile picture upload failed", "INTERNAL_ERROR"));
        }
    }
    
    @DeleteMapping("/account")
    public ResponseEntity<ApiResponse<Map<String, Object>>> deleteAccount(
            @Valid @RequestBody DeleteAccountRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            String ipAddress = getClientIp(httpRequest);
            
            SecurityService.ActivityContext activity = new SecurityService.ActivityContext(
                ipAddress, httpRequest.getHeader("User-Agent"), System.currentTimeMillis());
            
            SecurityService.SuspiciousActivityResult suspiciousResult = 
                securityService.detectSuspiciousActivity(userPayload.getId(), activity);
            
            if (suspiciousResult.getRiskScore() > 30) {
                return ResponseEntity.status(403).body(
                    ApiResponse.error("account deletion blocked due to suspicious activity", 
                        "SUSPICIOUS_ACTIVITY"));
            }
            
            UserService.DeletionResult result = userService.deleteAccount(
                userPayload.getId(), request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("accountDeleted", true);
            responseData.put("deletionScheduled", result.isDeletionScheduled());
            responseData.put("finalDeletionDate", result.getFinalDeletionDate());
            
            return ResponseEntity.ok(
                ApiResponse.success("account deletion initiated", responseData));
                
        } catch (Exception error) {
            log.error("Error deleting account: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("account deletion failed", "INTERNAL_ERROR"));
        }
    }
    
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Map<String, Object>>> searchUsers(
            @RequestParam String query,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int limit,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                userPayload.getId(), "user", "/users/search");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("search rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            UserService.SearchResult result = userService.searchUsers(query, page, limit);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("users", result.getUsers());
            responseData.put("totalCount", result.getTotalCount());
            responseData.put("currentPage", page);
            responseData.put("totalPages", result.getTotalPages());
            responseData.put("hasMore", result.isHasMore());
            
            return ResponseEntity.ok(
                ApiResponse.success("user search completed", responseData));
                
        } catch (Exception error) {
            log.error("Error searching users: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("user search failed", "INTERNAL_ERROR"));
        }
    }
    
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("status", "ok");
        healthData.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(
            ApiResponse.success("user service is running", healthData));
    }
    
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
