# PostgreSQL JDBC Integration Guide

This guide explains how to use the PostgreSQL JDBC services in the AeTrust Java backend, following the pattern from your example code.

## Overview

The PostgreSQL integration provides:
- **DatabaseService**: High-level database operations with Spring integration
- **PostgresJdbcService**: Low-level JDBC operations similar to your example
- **TransactionJdbcService**: Business-specific transaction operations
- **DatabaseConfig**: Environment-based configuration
- **DatabaseHealthController**: Health checks and monitoring

## Environment Configuration

Set these environment variables (see `.env.example`):

```bash
# Primary database connection
DATABASE_URL=*******************************************
DB_USERNAME=aetrust_user
DB_PASSWORD=aetrust_password

# Connection pool settings
DB_CONNECTION_POOL_SIZE=20
DB_MIN_IDLE=5
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000
DB_MAX_LIFETIME=1800000
```

## Usage Examples

### 1. Basic CRUD Operations (Similar to Your Example)

```java
@Autowired
private PostgresJdbcService postgresJdbcService;

// Get all records (like getAllBirds)
List<Map<String, Object>> users = postgresJdbcService.getAllRecords("users");

// Get filtered records (like getFilteredBirds)
List<Map<String, Object>> activeUsers = postgresJdbcService.getFilteredRecords("users", "status", "active");

// Insert record (like insertBird)
Map<String, Object> userData = new HashMap<>();
userData.put("username", "john_doe");
userData.put("email", "<EMAIL>");
userData.put("status", "active");
int inserted = postgresJdbcService.insertRecord("users", userData);

// Update record (like updateBird)
Map<String, Object> updateData = new HashMap<>();
updateData.put("status", "inactive");
int updated = postgresJdbcService.updateRecord("users", updateData, "username", "john_doe");

// Delete record (like deleteBird)
int deleted = postgresJdbcService.deleteRecord("users", "username", "john_doe");
```

### 2. Custom Queries with Parameters

```java
// Execute custom query
String sql = "SELECT * FROM transactions WHERE amount > ? AND created_at > ?";
List<Map<String, Object>> results = postgresJdbcService.executeCustomQuery(sql, 1000, "2024-01-01");

// Execute custom update
String updateSql = "UPDATE users SET last_login = NOW() WHERE id = ?";
int affected = postgresJdbcService.executeCustomUpdate(updateSql, userId);
```

### 3. Error Handling (Like Your Example)

The service automatically handles PostgreSQL error codes:

```java
try {
    postgresJdbcService.insertRecord("users", userData);
} catch (SQLException e) {
    String errorCode = e.getSQLState();
    // Error codes are automatically logged:
    // 08000 - connection_exception
    // 42601 - syntax_error  
    // 23505 - unique_violation
    // 23503 - foreign_key_violation
    // etc.
}
```

### 4. Transaction Operations

```java
@Autowired
private TransactionJdbcService transactionService;

// Create transaction
String txId = transactionService.createTransaction(
    "user123", 
    "credit", 
    new BigDecimal("100.00"), 
    "Payment received"
);

// Get user transactions
List<Map<String, Object>> userTxs = transactionService.getUserTransactions("user123");

// Get transaction summary
Map<String, Object> summary = transactionService.getUserTransactionSummary("user123");

// Update transaction status
boolean updated = transactionService.updateTransactionStatus(txId, "completed");
```

### 5. Batch Operations

```java
// Batch insert
List<Map<String, Object>> batchData = Arrays.asList(
    Map.of("name", "user1", "email", "<EMAIL>"),
    Map.of("name", "user2", "email", "<EMAIL>"),
    Map.of("name", "user3", "email", "<EMAIL>")
);
int[] results = postgresJdbcService.executeBatchInsert("users", batchData);
```

### 6. High-Level Database Service

```java
@Autowired
private DatabaseService databaseService;

// Health check
boolean isHealthy = databaseService.isHealthy();

// Execute with connection callback
String result = databaseService.executeWithConnection(conn -> {
    // Your custom JDBC code here
    PreparedStatement stmt = conn.prepareStatement("SELECT version()");
    ResultSet rs = stmt.executeQuery();
    rs.next();
    return rs.getString(1);
});

// Execute in transaction
String txResult = databaseService.executeInTransaction(() -> {
    // Multiple database operations in transaction
    return "success";
});
```

## API Endpoints

### Health Check Endpoints

```bash
# Database health
GET /api/v1/database/health

# Database info
GET /api/v1/database/info

# Test connection
GET /api/v1/database/test-connection

# List tables
GET /api/v1/database/tables

# Database statistics
GET /api/v1/database/stats
```

### Example CRUD Endpoints

```bash
# Get all records from table
GET /api/v1/postgres-example/tables/{tableName}/records

# Get filtered records
GET /api/v1/postgres-example/tables/{tableName}/records/filter?column=status&value=active

# Insert record
POST /api/v1/postgres-example/tables/{tableName}/records
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "status": "active"
}

# Update record
PUT /api/v1/postgres-example/tables/{tableName}/records?whereColumn=id&whereValue=123
{
  "status": "inactive"
}

# Delete record
DELETE /api/v1/postgres-example/tables/{tableName}/records?whereColumn=id&whereValue=123

# Execute custom query
POST /api/v1/postgres-example/query
{
  "sql": "SELECT * FROM users WHERE created_at > ?",
  "params": ["2024-01-01"]
}
```

### Transaction Endpoints

```bash
# Create transaction
POST /api/v1/postgres-example/transactions
{
  "user_id": "user123",
  "type": "credit",
  "amount": 100.00,
  "description": "Payment received"
}

# Get user transactions
GET /api/v1/postgres-example/transactions/user/{userId}
```

## Connection Pool Configuration

The service uses HikariCP for connection pooling with these optimizations:

```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        rewriteBatchedStatements: true
```

## Production Deployment

### Environment Variables for Different Platforms

**Render.com PostgreSQL:**
```bash
DATABASE_URL=**********************************************************************
DB_USERNAME=database_user
DB_PASSWORD=your_secure_password
```

**AWS RDS PostgreSQL:**
```bash
DATABASE_URL=************************************************************************************
DB_USERNAME=aetrust_user
DB_PASSWORD=your_secure_password
```

**Google Cloud SQL:**
```bash
DATABASE_URL=*****************************************************
DB_USERNAME=aetrust_user
DB_PASSWORD=your_secure_password
```

### SSL Configuration

For production, add SSL parameters to DATABASE_URL:
```bash
DATABASE_URL=*************************************************************
```

## Monitoring and Logging

The service provides comprehensive logging and monitoring:

- Connection pool statistics
- SQL error code handling
- Performance metrics
- Health checks
- Database metadata

Check logs for database operations:
```bash
tail -f logs/aetrust-backend.log | grep -E "(SQL|Database|Connection)"
```

## Best Practices

1. **Always use parameterized queries** to prevent SQL injection
2. **Handle SQLException properly** with error code checking
3. **Use connection pooling** for better performance
4. **Monitor connection pool** statistics in production
5. **Set appropriate timeouts** for database operations
6. **Use transactions** for multi-step operations
7. **Implement retry logic** for transient failures
8. **Log database operations** for debugging and monitoring

## Error Handling

The service handles common PostgreSQL error codes:

- `08000` - Connection exception (retry recommended)
- `42601` - Syntax error (fix query)
- `42501` - Insufficient privilege (check permissions)
- `23505` - Unique violation (handle duplicate)
- `23503` - Foreign key violation (check references)
- `23502` - Not null violation (provide required fields)

## Performance Tips

1. Use batch operations for multiple inserts
2. Enable prepared statement caching
3. Use appropriate connection pool sizes
4. Monitor slow queries
5. Use database indexes effectively
6. Implement query result caching where appropriate
7. Use read replicas for read-heavy workloads
