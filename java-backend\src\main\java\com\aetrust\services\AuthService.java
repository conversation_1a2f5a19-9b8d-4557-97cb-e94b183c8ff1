package com.aetrust.services;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.models.User;
import com.aetrust.types.Types.*;
import com.aetrust.utils.CryptoUtils;
import com.aetrust.utils.JwtUtils;
import com.aetrust.utils.RetryUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.aetrust.repositories.UserRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AuthService {
    
    @Autowired
    private UserRepository userRepository;
    
    // Global cache service - auto-switches between Redis and in-memory
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private CryptoUtils cryptoUtils;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private NotificationService notificationService;
    
    public LoginResult login(LoginRequest request, String ipAddress, String userAgent) {
        try {
            User user = RetryUtils.withDatabaseRetry(() -> {
                return userRepository.findByEmailAndDeletedAtIsNullAndAccountStatusNot(
                    request.getEmail(), AccountStatus.CLOSED).orElse(null);
            }, "user-login-lookup");
            
            if (user == null) {
                return LoginResult.error("invalid email or password", "INVALID_CREDENTIALS");
            }
            
            if (!cryptoUtils.verifyPassword(request.getPassword(), user.getPassword())) {
                return LoginResult.error("invalid email or password", "INVALID_CREDENTIALS");
            }
            
            if (user.getAccountStatus() == AccountStatus.SUSPENDED) {
                return LoginResult.error("account is suspended", "ACCOUNT_SUSPENDED");
            }
            
            if (user.getAccountStatus() == AccountStatus.INACTIVE) {
                return LoginResult.error("account is inactive", "ACCOUNT_INACTIVE");
            }
            
            // Update user login information
            RetryUtils.withDatabaseRetry(() -> {
                user.getSecurity().setLastLogin(LocalDateTime.now());
                user.getSecurity().setLastLoginIp(ipAddress);
                user.setUpdatedAt(LocalDateTime.now());
                userRepository.save(user);
                return null;
            }, "user-login-update");
            
            String accessToken = jwtUtils.generateToken(
                user.getId().toString(), user.getEmail(), user.getRole(),
                user.isVerified(), user.getKycStatus());
            String refreshToken = jwtUtils.generateRefreshToken(user.getId().toString());
            
            String refreshKey = "refresh_token:" + user.getId();
            cacheService.set(refreshKey, refreshToken, 7, TimeUnit.DAYS);
            
            Map<String, Object> userData = createUserData(user);
            
            log.info("User logged in successfully: {}", cryptoUtils.maskSensitiveData(user.getEmail(), 2));
            
            return LoginResult.success("login successful", accessToken, refreshToken, userData);
            
        } catch (Exception error) {
            log.error("Error during login: {}", error.getMessage());
            return LoginResult.error("login failed", "INTERNAL_ERROR");
        }
    }
    
    public RefreshResult refreshToken(String refreshToken) {
        try {
            JwtUtils.UserPayload payload = jwtUtils.extractUserPayload(refreshToken);
            
            String refreshKey = "refresh_token:" + payload.getId();
            String storedToken = cacheService.get(refreshKey);
            
            if (storedToken == null || !storedToken.equals(refreshToken)) {
                return RefreshResult.error("invalid refresh token", "INVALID_REFRESH_TOKEN");
            }
            
            // get user
            Long userId;
            try {
                userId = Long.parseLong(payload.getId());
            } catch (NumberFormatException e) {
                return RefreshResult.error("invalid user id", "INVALID_USER_ID");
            }

            User user = userRepository.findById(userId)
                .filter(u -> u.getDeletedAt() == null)
                .orElse(null);
            
            if (user == null) {
                return RefreshResult.error("user not found", "USER_NOT_FOUND");
            }
            

            String newAccessToken = jwtUtils.generateToken(
                user.getId().toString(), user.getEmail(), user.getRole(),
                user.isVerified(), user.getKycStatus());
            String newRefreshToken = jwtUtils.generateRefreshToken(user.getId().toString());
            
            cacheService.set(refreshKey, newRefreshToken, 7, TimeUnit.DAYS);
            
            return RefreshResult.success("token refreshed", newAccessToken, newRefreshToken);
            
        } catch (Exception error) {
            log.error("Error refreshing token: {}", error.getMessage());
            return RefreshResult.error("token refresh failed", "INTERNAL_ERROR");
        }
    }
    
    public LogoutResult logout(String accessToken, String refreshToken) {
        try {
            if (refreshToken != null) {
                try {
                    JwtUtils.UserPayload payload = jwtUtils.extractUserPayload(refreshToken);
                    String refreshKey = "refresh_token:" + payload.getId();
                    cacheService.delete(refreshKey);
                } catch (Exception e) {
                    log.warn("Error clearing refresh token during logout: {}", e.getMessage());
                }
            }
            
            if (accessToken != null) {
                // blacklist access token
                String tokenKey = "blacklisted_token:" + accessToken.hashCode();
                cacheService.set(tokenKey, "true", 1, TimeUnit.HOURS);
            }
            
            return LogoutResult.success("logout successful");
            
        } catch (Exception error) {
            log.error("Error during logout: {}", error.getMessage());
            return LogoutResult.error("logout failed", "INTERNAL_ERROR");
        }
    }
    
    public ForgotPasswordResult forgotPassword(String email) {
        try {
            User user = userRepository.findByEmailAndDeletedAtIsNull(email).orElse(null);
            
            if (user == null) {
                return ForgotPasswordResult.success("reset email sent", true);
            }
            
            String resetToken = cryptoUtils.generateSecureToken(32);
            String resetKey = "password_reset:" + resetToken;
            
            cacheService.set(resetKey, user.getId().toString(), 15, TimeUnit.MINUTES);
            
            String resetLink = "https://app.aetrust.com/reset-password?token=" + resetToken;
            String emailBody = "Click the link to reset your password: " + resetLink;
            
            notificationService.sendEmail(user.getEmail(), "Password Reset", emailBody);
            
            log.info("Password reset requested for: {}", cryptoUtils.maskSensitiveData(email, 2));
            
            return ForgotPasswordResult.success("reset email sent", true);
            
        } catch (Exception error) {
            log.error("Error in forgot password: {}", error.getMessage());
            return ForgotPasswordResult.success("reset email sent", true); // always return success
        }
    }
    
    public ResetPasswordResult resetPassword(String token, String newPassword) {
        try {
            String resetKey = "password_reset:" + token;
            String userId = cacheService.get(resetKey);
            
            if (userId == null) {
                return ResetPasswordResult.error("invalid or expired reset token", "INVALID_TOKEN");
            }
            
            // get user
            Long userIdLong = Long.parseLong(userId);
            User user = userRepository.findById(userIdLong).orElse(null);

            if (user == null) {
                return ResetPasswordResult.error("user not found", "USER_NOT_FOUND");
            }

            String passwordHash = cryptoUtils.hashPassword(newPassword);
            user.setPassword(passwordHash);
            user.setUpdatedAt(LocalDateTime.now());
            userRepository.save(user);
            
            cacheService.delete(resetKey);

            String refreshKey = "refresh_token:" + userId;
            cacheService.delete(refreshKey);
            
            log.info("Password reset completed for user: {}", cryptoUtils.maskSensitiveData(user.getEmail(), 2));
            
            return ResetPasswordResult.success("password reset successful");
            
        } catch (Exception error) {
            log.error("Error resetting password: {}", error.getMessage());
            return ResetPasswordResult.error("password reset failed", "INTERNAL_ERROR");
        }
    }
    
    private Map<String, Object> createUserData(User user) {
        Map<String, Object> userData = new HashMap<>();
        userData.put("id", user.getId());
        userData.put("email", user.getEmail());
        userData.put("phone", user.getPhone());
        userData.put("first_name", user.getFirstName());
        userData.put("last_name", user.getLastName());
        userData.put("full_name", user.getFullName());
        userData.put("role", user.getRole().getValue());
        userData.put("is_verified", user.isVerified());
        userData.put("kyc_status", user.getKycStatus().getValue());
        userData.put("wallet_balance", user.getWalletBalance());
        userData.put("created_at", user.getCreatedAt());
        return userData;
    }

    // result classes
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoginResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String accessToken;
        private String refreshToken;
        private Map<String, Object> userData;
        private boolean requires2FA = false;
        private String challengeToken;

        public static LoginResult success(String message, String accessToken, String refreshToken, Map<String, Object> userData) {
            return new LoginResult(true, message, null, accessToken, refreshToken, userData, false, null);
        }

        public static LoginResult error(String message, String errorCode) {
            return new LoginResult(false, message, errorCode, null, null, null, false, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefreshResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String accessToken;
        private String refreshToken;

        public static RefreshResult success(String message, String accessToken, String refreshToken) {
            return new RefreshResult(true, message, null, accessToken, refreshToken);
        }

        public static RefreshResult error(String message, String errorCode) {
            return new RefreshResult(false, message, errorCode, null, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LogoutResult {
        private boolean success;
        private String message;
        private String errorCode;

        public static LogoutResult success(String message) {
            return new LogoutResult(true, message, null);
        }

        public static LogoutResult error(String message, String errorCode) {
            return new LogoutResult(false, message, errorCode);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForgotPasswordResult {
        private boolean success;
        private String message;
        private String errorCode;
        private boolean emailSent;

        public static ForgotPasswordResult success(String message, boolean emailSent) {
            return new ForgotPasswordResult(true, message, null, emailSent);
        }

        public static ForgotPasswordResult error(String message, String errorCode) {
            return new ForgotPasswordResult(false, message, errorCode, false);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResetPasswordResult {
        private boolean success;
        private String message;
        private String errorCode;

        public static ResetPasswordResult success(String message) {
            return new ResetPasswordResult(true, message, null);
        }

        public static ResetPasswordResult error(String message, String errorCode) {
            return new ResetPasswordResult(false, message, errorCode);
        }
    }
}
