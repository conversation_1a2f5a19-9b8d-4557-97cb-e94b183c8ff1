package com.aetrust.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Arrays;

@Slf4j
@Component
public class CryptoUtils {
    
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String KEY_ALGORITHM = "AES";
    private static final int KEY_LENGTH = 32;
    private static final int IV_LENGTH = 16;
    
    @Value("${aetrust.security.salt-rounds:12}")
    private int saltRounds;
    
    @Value("${aetrust.security.encryption-key}")
    private String encryptionKey;
    
    private final BCryptPasswordEncoder passwordEncoder;
    private final SecureRandom secureRandom;
    
    public CryptoUtils() {
        this.passwordEncoder = new BCryptPasswordEncoder();
        this.secureRandom = new SecureRandom();
    }
    
  
    public String hashPassword(String password) {
        try {
            return passwordEncoder.encode(password);
        } catch (Exception error) {
            log.error("Failed to hash password: {}", error.getMessage());
            throw new RuntimeException("Failed to hash password");
        }
    }
    
   
    public boolean verifyPassword(String password, String hash) {
        try {
            return passwordEncoder.matches(password, hash);
        } catch (Exception error) {
            log.error("Password verification failed: {}", error.getMessage());
            return false;
        }
    }
    
   
    public String generateRandomString(int length) {
        byte[] bytes = new byte[length];
        secureRandom.nextBytes(bytes);
        return bytesToHex(bytes);
    }
    
   
    public int generateRandomNumber(int min, int max) {
        return secureRandom.nextInt(max - min + 1) + min;
    }
    
    
    public String encrypt(String text, String key) {
        try {
            String keyToUse = key != null ? key : encryptionKey;
            SecretKeySpec secretKey = deriveKey(keyToUse);
            
            byte[] iv = new byte[IV_LENGTH];
            secureRandom.nextBytes(iv);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
            
            byte[] encrypted = cipher.doFinal(text.getBytes(StandardCharsets.UTF_8));
            
            return bytesToHex(iv) + ":" + bytesToHex(encrypted);
        } catch (Exception error) {
            log.error("Encryption failed: {}", error.getMessage());
            throw new RuntimeException("Encryption failed");
        }
    }
    
    
    public String decrypt(String encryptedText, String key) {
        try {
            String keyToUse = key != null ? key : encryptionKey;
            SecretKeySpec secretKey = deriveKey(keyToUse);
            
            String[] parts = encryptedText.split(":");
            if (parts.length != 2) {
                throw new IllegalArgumentException("Invalid encrypted text format");
            }
            
            byte[] iv = hexToBytes(parts[0]);
            byte[] encrypted = hexToBytes(parts[1]);
            
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
            
            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception error) {
            log.error("Decryption failed: {}", error.getMessage());
            throw new RuntimeException("Decryption failed");
        }
    }
    
   
    public String generateHash(String data, String algorithm) {
        try {
            String alg = algorithm != null ? algorithm : "SHA-256";
            MessageDigest digest = MessageDigest.getInstance(alg);
            byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hash);
        } catch (Exception error) {
            log.error("Hash generation failed: {}", error.getMessage());
            throw new RuntimeException("Hash generation failed");
        }
    }
    
    
    public String generateHMAC(String data, String secret, String algorithm) {
        try {
            String alg = algorithm != null ? algorithm : "HmacSHA256";
            String key = secret != null ? secret : encryptionKey;
            
            Mac mac = Mac.getInstance(alg);
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), alg);
            mac.init(secretKeySpec);
            
            byte[] hmac = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hmac);
        } catch (Exception error) {
            log.error("HMAC generation failed: {}", error.getMessage());
            throw new RuntimeException("HMAC generation failed");
        }
    }
    
  
    public String maskSensitiveData(String data, int visibleChars) {
        if (data == null || data.length() <= visibleChars * 2) {
            return "*".repeat(Math.max(data != null ? data.length() : 0, 4));
        }
        
        String start = data.substring(0, visibleChars);
        String end = data.substring(data.length() - visibleChars);
        String middle = "*".repeat(data.length() - (visibleChars * 2));
        
        return start + middle + end;
    }
    

    public String generateOTP(int length) {
        StringBuilder otp = new StringBuilder();
        for (int i = 0; i < length; i++) {
            otp.append(secureRandom.nextInt(10));
        }
        return otp.toString();
    }
    
    
    public String generateSecureToken(int length) {
        byte[] bytes = new byte[length];
        secureRandom.nextBytes(bytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
    }
    
   
    public boolean constantTimeCompare(String a, String b) {
        if (a == null || b == null) return false;
        if (a.length() != b.length()) return false;
        
        byte[] aBytes = a.getBytes(StandardCharsets.UTF_8);
        byte[] bBytes = b.getBytes(StandardCharsets.UTF_8);
        
        return MessageDigest.isEqual(aBytes, bBytes);
    }
    

    private SecretKeySpec deriveKey(String key) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] keyBytes = digest.digest(key.getBytes(StandardCharsets.UTF_8));
        byte[] derivedKey = Arrays.copyOf(keyBytes, KEY_LENGTH);
        return new SecretKeySpec(derivedKey, KEY_ALGORITHM);
    }
    
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    private byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }
}
