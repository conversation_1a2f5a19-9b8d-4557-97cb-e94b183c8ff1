{"info": {"name": "AeTrust  Backend API", "description": "Comprehensive API collection for AeTrust  backend with modular user architecture", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080/api/v1", "type": "string", "description": " Backend Base URL"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "userUuid", "value": "", "type": "string"}, {"key": "registrationId", "value": "", "type": "string"}, {"key": "walletId", "value": "", "type": "string"}], "item": [{"name": " Registration Flow", "description": "Complete  registration flow with modular architecture", "item": [{"name": "1. Initiate Registration", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.registrationId) {", "        pm.collectionVariables.set('registrationId', response.data.registrationId);", "        console.log('Registration ID saved:', response.data.registrationId);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+2507049670618\",\n  \"firstName\": \"\",\n  \"lastName\": \"Test\",\n  \"password\": \"SecurePass123!\",\n  \"userType\": \"individual\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/registration/initiate", "host": ["{{baseUrl}}"], "path": ["registration", "initiate"]}}}, {"name": "2. Verify Phone", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.accessToken) {", "        pm.collectionVariables.set('authToken', response.data.accessToken);", "        pm.collectionVariables.set('userUuid', response.data.user.userUuid);", "        console.log('Auth token and UUID saved');", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+2507049670618\",\n  \"code\": \"123456\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/registration/verify-phone", "host": ["{{baseUrl}}"], "path": ["registration", "verify-phone"]}}}, {"name": "3. <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"code\": \"123456\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/registration/verify-email", "host": ["{{baseUrl}}"], "path": ["registration", "verify-email"]}}}, {"name": "4. Complete Personal Info", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"\",\n  \"lastName\": \"Test\",\n  \"dateOfBirth\": \"1990-01-01\",\n  \"bio\": \" backend test user\",\n  \"address\": {\n    \"street\": \"123  Street\",\n    \"city\": \"Kigali\",\n    \"state\": \"Kigali\",\n    \"country\": \"Rwanda\",\n    \"postalCode\": \"00000\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/registration/personal-info", "host": ["{{baseUrl}}"], "path": ["registration", "personal-info"]}}}, {"name": "5. Submit Identity Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"idType\": \"NATIONAL_ID\",\n  \"idNumber\": \"1234567890123456\",\n  \"idDocumentFront\": \"base64_encoded_front_image\",\n  \"idDocumentBack\": \"base64_encoded_back_image\",\n  \"selfiePhoto\": \"base64_encoded_selfie\"\n}"}, "url": {"raw": "{{baseUrl}}/registration/identity-verification", "host": ["{{baseUrl}}"], "path": ["registration", "identity-verification"]}}}, {"name": "6. Set Transaction PIN", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"pin\": \"1234\",\n  \"confirmPin\": \"1234\"\n}"}, "url": {"raw": "{{baseUrl}}/registration/transaction-pin", "host": ["{{baseUrl}}"], "path": ["registration", "transaction-pin"]}}}, {"name": "7. Enable Biometric", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"biometricEnabled\": true,\n  \"enrollmentType\": \"fingerprint\"\n}"}, "url": {"raw": "{{baseUrl}}/registration/biometric-enrollment", "host": ["{{baseUrl}}"], "path": ["registration", "biometric-enrollment"]}}}, {"name": "8. Business Verification (Agents)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"businessName\": \" Test Store\",\n  \"businessType\": \"RETAIL\",\n  \"businessRegistrationNumber\": \"REG123456789\",\n  \"businessAddress\": \"123 Business Street, Kigali, Rwanda\",\n  \"businessDocument\": \"base64_encoded_document\",\n  \"taxCertificate\": \"base64_encoded_certificate\"\n}"}, "url": {"raw": "{{baseUrl}}/registration/business-verification", "host": ["{{baseUrl}}"], "path": ["registration", "business-verification"]}}}, {"name": "Get Registration Progress", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/registration/progress", "host": ["{{baseUrl}}"], "path": ["registration", "progress"]}}}, {"name": "Get Registration Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/registration/status?phone=+2507049670618", "host": ["{{baseUrl}}"], "path": ["registration", "status"], "query": [{"key": "phone", "value": "+2507049670618"}]}}}, {"name": "Resend Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"identifier\": \"<EMAIL>\",\n  \"verificationType\": \"email\"\n}"}, "url": {"raw": "{{baseUrl}}/registration/resend-verification", "host": ["{{baseUrl}}"], "path": ["registration", "resend-verification"]}}}]}, {"name": " User Management", "description": "User management endpoints using modular architecture", "item": [{"name": "Get Current User (Complete Data)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"Updated\",\n  \"lastName\": \"Name\",\n  \"bio\": \"Updated bio for  backend\",\n  \"address\": {\n    \"street\": \"456 Updated Street\",\n    \"city\": \"Kigali\",\n    \"state\": \"Kigali\",\n    \"country\": \"Rwanda\",\n    \"postalCode\": \"12345\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/users/profile", "host": ["{{baseUrl}}"], "path": ["users", "profile"]}}}, {"name": "Update Password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"SecurePass123!\",\n  \"newPassword\": \"NewSecurePass123!\",\n  \"confirmPassword\": \"NewSecurePass123!\"\n}"}, "url": {"raw": "{{baseUrl}}/users/password", "host": ["{{baseUrl}}"], "path": ["users", "password"]}}}, {"name": "Upload Profile Picture", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"imageData\": \"base64_encoded_image_data_here\",\n  \"fileName\": \"profile.jpg\",\n  \"mimeType\": \"image/jpeg\"\n}"}, "url": {"raw": "{{baseUrl}}/users/profile-picture", "host": ["{{baseUrl}}"], "path": ["users", "profile-picture"]}}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"SecurePass123!\",\n  \"reason\": \"Testing account deletion\",\n  \"confirmDeletion\": true\n}"}, "url": {"raw": "{{baseUrl}}/users/account", "host": ["{{baseUrl}}"], "path": ["users", "account"]}}}, {"name": "Search Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/search?query=&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["users", "search"], "query": [{"key": "query", "value": ""}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get User Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/stats", "host": ["{{baseUrl}}"], "path": ["users", "stats"]}}}, {"name": "Verify Email <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/verify/email_verification_token_here", "host": ["{{baseUrl}}"], "path": ["users", "verify", "email_verification_token_here"]}}}, {"name": "User Service Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/health", "host": ["{{baseUrl}}"], "path": ["users", "health"]}}}]}, {"name": " Dashboard Endpoints", "description": "Dashboard and analytics endpoints with modular data", "item": [{"name": "Get Basic Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard", "host": ["{{baseUrl}}"], "path": ["users", "dashboard"]}}}, {"name": "Get Enhanced Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard/enhanced", "host": ["{{baseUrl}}"], "path": ["users", "dashboard", "enhanced"]}}}, {"name": "Get Dashboard Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard/profile", "host": ["{{baseUrl}}"], "path": ["users", "dashboard", "profile"]}}}, {"name": "Get Wallet Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard/wallet", "host": ["{{baseUrl}}"], "path": ["users", "dashboard", "wallet"]}}}, {"name": "Get Account Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard/status", "host": ["{{baseUrl}}"], "path": ["users", "dashboard", "status"]}}}]}, {"name": " Modular Operations", "description": "Direct modular operations for testing the new architecture", "item": [{"name": "Create User Wallet", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.walletId) {", "        pm.collectionVariables.set('walletId', response.data.walletId);", "        console.log('Wallet ID saved:', response.data.walletId);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"walletType\": \"SAVINGS\",\n  \"currency\": \"USD\",\n  \"isDefault\": false\n}"}, "url": {"raw": "{{baseUrl}}/users/wallets/create", "host": ["{{baseUrl}}"], "path": ["users", "wallets", "create"]}}}, {"name": "Update Wallet Balance", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100.00,\n  \"transactionType\": \"CREDIT\",\n  \"description\": \"Test credit transaction\"\n}"}, "url": {"raw": "{{baseUrl}}/users/wallets/{{walletId}}/balance", "host": ["{{baseUrl}}"], "path": ["users", "wallets", "{{walletId}}", "balance"]}}}, {"name": "Update Security Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactionPin\": \"1234\",\n  \"biometricEnabled\": true,\n  \"twoFactorEnabled\": true\n}"}, "url": {"raw": "{{baseUrl}}/users/security/update", "host": ["{{baseUrl}}"], "path": ["users", "security", "update"]}}}, {"name": "Update User Preferences", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"language\": \"en\",\n  \"currency\": \"USD\",\n  \"timezone\": \"Africa/Kigali\",\n  \"notificationSettings\": {\n    \"email\": true,\n    \"sms\": true,\n    \"push\": false\n  }\n}"}, "url": {"raw": "{{baseUrl}}/users/preferences/update", "host": ["{{baseUrl}}"], "path": ["users", "preferences", "update"]}}}]}]}