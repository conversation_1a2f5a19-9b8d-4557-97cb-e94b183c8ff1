# GitLab CI/CD Pipeline for AeTrust Backend (Development)
#
# Required CI/CD Variables (set in GitLab project settings):
# - CI_MONGODB_URI: MongoDB connection string for tests
# - CI_MONGODB_URI_E2E: MongoDB connection string for E2E tests
# - CI_REDIS_URL: Redis connection string
# - CI_JWT_SECRET: JWT secret key for tests
# - CI_BCRYPT_ROUNDS: Bcrypt rounds for password hashing (optional, defaults to 4 for tests)
# - CI_BASE_URL: Base URL for E2E tests (optional, defaults to http://localhost:3000)
# - DEV_MONGODB_URI: MongoDB connection string for development deployment
# - DEV_REDIS_URL: Redis connection string for development deployment
# - DEV_JWT_SECRET: JWT secret key for development deployment
# - DEV_BCRYPT_ROUNDS: Bcrypt rounds for development (optional, defaults to 10)

stages:
  - test
  - build
  - security
  - deploy

variables:
  NODE_VERSION: "22"
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  NODE_ENV: "test"
  PORT: "3000"

cache:
  paths:
    - node_modules/
    - .npm/

before_script:
  - apt-get update -qq && apt-get install -y -qq git curl
  - curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
  - apt-get install -y nodejs
  - npm ci --cache .npm --prefer-offline

# Test stage
test:unit:
  stage: test
  script:
    - npm run test:unit
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
      junit: coverage/junit.xml
    paths:
      - coverage/
    expire_in: 1 week
  only:
    - merge_requests
    - develop 

test:integration:
  stage: test
  services:
    - mongo:5.0
    - redis:6.2
  variables:
    MONGODB_URI: "${CI_MONGODB_URI:-mongodb://mongo:27017/aetrust_test}"
    REDIS_URL: "${CI_REDIS_URL:-redis://redis:6379}"
    NODE_ENV: "test"
    JWT_SECRET: "${CI_JWT_SECRET:-test-jwt-secret-key-for-integration}"
    BCRYPT_ROUNDS: "${CI_BCRYPT_ROUNDS:-4}"
  script:
    - npm run test:integration
  artifacts:
    reports:
      junit: test-results/integration-junit.xml
    paths:
      - test-results/
    expire_in: 1 week
  only:
    - merge_requests
    - develop  

test:e2e:
  stage: test
  services:
    - mongo:5.0
    - redis:6.2
  variables:
    MONGODB_URI: "${CI_MONGODB_URI_E2E:-mongodb://mongo:27017/aetrust_e2e}"
    REDIS_URL: "${CI_REDIS_URL:-redis://redis:6379}"
    NODE_ENV: "test"
    JWT_SECRET: "${CI_JWT_SECRET:-test-jwt-secret-key-for-e2e}"
    BCRYPT_ROUNDS: "${CI_BCRYPT_ROUNDS:-4}"
    BASE_URL: "${CI_BASE_URL:-http://localhost:3000}"
  script:
    - npm run test:e2e
  artifacts:
    reports:
      junit: test-results/e2e-junit.xml
    paths:
      - test-results/
      - screenshots/
    expire_in: 1 week
  only:
    - merge_requests
    - develop 

# Lint and type checking
lint:
  stage: test
  script:
    - npm run lint
    - npm run type-check
  artifacts:
    reports:
      codequality: gl-codequality.json
  only:
    - merge_requests
    - develop  

# Security scanning
security:dependency-scan:
  stage: security
  script:
    - npm audit --audit-level=moderate
    - npm run security:check
  allow_failure: true
  only:
    - merge_requests
    - develop  

# Build stage
build:
  stage: build
  script:
    - npm run build
  artifacts:
    paths:
      - dist/
    expire_in: 1 week
  only:
    - develop 

# Docker build
build:docker:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  variables:
    DOCKER_IMAGE_TAG: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $DOCKER_IMAGE_TAG .
    - docker push $DOCKER_IMAGE_TAG
    - |
      if [ "$CI_COMMIT_REF_NAME" = "main" ]; then
        docker tag $DOCKER_IMAGE_TAG $CI_REGISTRY_IMAGE:latest
        docker push $CI_REGISTRY_IMAGE:latest
      fi
  only:
    - main
    - develop
    - tags

# Deploy 
deploy:development:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - echo "Deploying to development environment..."
    - echo "Image: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
    - echo "Environment: development"

  environment:
    name: development
    url: https://dev-api.aetrust.com
  variables:
    NODE_ENV: "development"
    MONGODB_URI: "${DEV_MONGODB_URI}"
    REDIS_URL: "${DEV_REDIS_URL}"
    JWT_SECRET: "${DEV_JWT_SECRET}"
    BCRYPT_ROUNDS: "${DEV_BCRYPT_ROUNDS:-10}"
  only:
    - develop
  when: manual

# Performance
performance:
  stage: test
  image: loadimpact/k6:latest
  script:
    - k6 run --out json=performance-results.json tests/performance/load-test.js
  artifacts:
    reports:
      performance: performance-results.json
    paths:
      - performance-results.json
    expire_in: 1 week
  only:
    - main
    - develop
  when: manual

# Database migration
migrate:development:
  stage: deploy
  script:
    - npm run migrate:development
  environment:
    name: development
  variables:
    NODE_ENV: "development"
    MONGODB_URI: "${DEV_MONGODB_URI}"
  only:
    - develop
  when: manual
  allow_failure: false


cleanup:
  stage: deploy
  script:
    - echo "Cleaning up old artifacts and images..."
  after_script:
    - docker system prune -f
  only:
    - schedules
  when: always
