package com.aetrust.models;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_security", indexes = {
    @Index(name = "idx_user_security_user_id", columnList = "userId"),
    @Index(name = "idx_user_security_uuid", columnList = "userUuid"),
    @Index(name = "idx_user_security_reset_token", columnList = "passwordResetToken"),
    @Index(name = "idx_user_security_email_token", columnList = "emailVerificationToken")
})
@EntityListeners(AuditingEntityListener.class)
public class UserSecurity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false, unique = true)
    private Long userId;

    @Column(name = "user_uuid", nullable = false, unique = true)
    private UUID userUuid;

    @JsonIgnore
    @Column(name = "transaction_pin_hash")
    private String transactionPinHash;

    @Column(name = "transaction_pin_set")
    private boolean transactionPinSet = false;

    @Column(name = "biometric_enabled")
    private boolean biometricEnabled = false;

    @Column(name = "biometric_enrolled_at")
    private LocalDateTime biometricEnrolledAt;

    @Column(name = "two_factor_enabled")
    private boolean twoFactorEnabled = false;

    @Column(name = "login_attempts")
    private Integer loginAttempts = 0;

    @Column(name = "locked_until")
    private LocalDateTime lockedUntil;

    @JsonIgnore
    @Column(name = "password_reset_token")
    private String passwordResetToken;

    @Column(name = "password_reset_expires")
    private LocalDateTime passwordResetExpires;

    @JsonIgnore
    @Column(name = "email_verification_token")
    private String emailVerificationToken;

    @Column(name = "email_verification_expires")
    private LocalDateTime emailVerificationExpires;

    @JsonIgnore
    @Column(name = "phone_verification_code", length = 10)
    private String phoneVerificationCode;

    @Column(name = "phone_verification_expires")
    private LocalDateTime phoneVerificationExpires;

    @Column(name = "last_login")
    private LocalDateTime lastLogin;

    @Column(name = "last_login_ip")
    private String lastLoginIp;

    @Column(name = "last_login_platform", length = 50)
    private String lastLoginPlatform;

    @Column(name = "pin_attempts")
    private Integer pinAttempts = 0;

    @Column(name = "pin_locked_until")
    private LocalDateTime pinLockedUntil;

    @CreatedDate
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    public boolean isAccountLocked() {
        return lockedUntil != null && lockedUntil.isAfter(LocalDateTime.now());
    }

    public boolean isPinLocked() {
        return pinLockedUntil != null && pinLockedUntil.isAfter(LocalDateTime.now());
    }

    public boolean isPasswordResetTokenValid() {
        return passwordResetToken != null && 
               passwordResetExpires != null && 
               passwordResetExpires.isAfter(LocalDateTime.now());
    }

    public boolean isEmailVerificationTokenValid() {
        return emailVerificationToken != null && 
               emailVerificationExpires != null && 
               emailVerificationExpires.isAfter(LocalDateTime.now());
    }

    public boolean isPhoneVerificationCodeValid() {
        return phoneVerificationCode != null && 
               phoneVerificationExpires != null && 
               phoneVerificationExpires.isAfter(LocalDateTime.now());
    }

    public void incrementLoginAttempts() {
        loginAttempts = (loginAttempts == null) ? 1 : loginAttempts + 1;
    }

    public void resetLoginAttempts() {
        loginAttempts = 0;
        lockedUntil = null;
    }

    public void incrementPinAttempts() {
        pinAttempts = (pinAttempts == null) ? 1 : pinAttempts + 1;
    }

    public void resetPinAttempts() {
        pinAttempts = 0;
        pinLockedUntil = null;
    }

    public void lockAccount(int minutes) {
        lockedUntil = LocalDateTime.now().plusMinutes(minutes);
    }

    public void lockPin(int minutes) {
        pinLockedUntil = LocalDateTime.now().plusMinutes(minutes);
    }

    public void updateLastLogin(String ipAddress, String platform) {
        lastLogin = LocalDateTime.now();
        lastLoginIp = ipAddress;
        lastLoginPlatform = platform;
    }
}
