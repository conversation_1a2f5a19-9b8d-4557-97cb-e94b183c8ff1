package com.aetrust.dto;

import com.aetrust.validation.Validation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.validation.constraints.*;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.math.BigDecimal;

public class RequestDTOs {
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegistrationInitRequest {
        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        @Size(max = 100, message = "email too long")
        private String email;
        
        @NotBlank(message = "phone number is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;
        
        @NotBlank(message = "first name is required")
        @Size(min = 2, max = 50, message = "first name must be between 2 and 50 characters")
        @Pattern(regexp = "^[a-zA-Z\\s\\-']+$", message = "first name contains invalid characters")
        private String firstName;
        
        @NotBlank(message = "last name is required")
        @Size(min = 2, max = 50, message = "last name must be between 2 and 50 characters")
        @Pattern(regexp = "^[a-zA-Z\\s\\-']+$", message = "last name contains invalid characters")
        private String lastName;
        
        @NotBlank(message = "password is required")
        @ValidPassword(message = "password must be at least 8 characters with uppercase, lowercase, number and special character")
        private String password;
        
        @NotBlank(message = "user type is required")
        @Pattern(regexp = "^(individual|business)$", message = "user type must be individual or business")
        private String userType;
        
        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
        
        @Past(message = "date of birth must be in the past")
        private LocalDate dateOfBirth;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PhoneVerificationRequest {
        @NotBlank(message = "phone number is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;
        
        @NotBlank(message = "verification code is required")
        @ValidVerificationCode(message = "verification code must be 6 digits")
        private String code;
        
        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmailVerificationRequest {
        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        private String email;
        
        @NotBlank(message = "verification code is required")
        @ValidVerificationCode(message = "verification code must be 6 digits")
        private String code;
        
        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegistrationCompleteRequest {
        @NotBlank(message = "phone is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;
        
        @NotBlank(message = "transaction PIN is required")
        @ValidPin(message = "PIN must be 4 digits")
        private String pin;
        
        @NotBlank(message = "PIN confirmation is required")
        @ValidPin(message = "PIN confirmation must be 4 digits")
        private String confirmPin;
        
        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
        
        @AssertTrue(message = "PIN and confirmation must match")
        public boolean isPinMatching() {
            return pin != null && pin.equals(confirmPin);
        }
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResendCodeRequest {
        @NotBlank(message = "phone is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;
        
        @NotBlank(message = "code type is required")
        @Pattern(regexp = "^(sms|email)$", message = "code type must be sms or email")
        private String codeType;
        
        @NotBlank(message = "platform is required")
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoginRequest {
        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        private String email;
        
        @NotBlank(message = "password is required")
        private String password;
        
        @Pattern(regexp = "^\\d{6}$", message = "2FA code must be 6 digits")
        private String twoFactorCode;
        
        @ValidPlatform(message = "invalid platform")
        private String platform;
        
        private String deviceInfo;
        private String challengeToken;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateProfileRequest {
        @Size(min = 2, max = 50, message = "first name must be between 2 and 50 characters")
        @Pattern(regexp = "^[a-zA-Z\\s\\-']*$", message = "first name contains invalid characters")
        private String firstName;
        
        @Size(min = 2, max = 50, message = "last name must be between 2 and 50 characters")
        @Pattern(regexp = "^[a-zA-Z\\s\\-']*$", message = "last name contains invalid characters")
        private String lastName;
        
        @Size(max = 50, message = "username too long")
        @Pattern(regexp = "^[a-zA-Z0-9_]*$", message = "username can only contain letters, numbers and underscores")
        private String username;
        
        @Size(max = 500, message = "bio too long")
        private String bio;
        
        @Past(message = "date of birth must be in the past")
        private LocalDate dateOfBirth;
        
        private AddressDto address;
        
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class AddressDto {
            @Size(max = 500, message = "street address too long")
            private String street;
            
            @Size(max = 100, message = "city name too long")
            private String city;
            
            @Size(max = 100, message = "state name too long")
            private String state;
            
            @Size(max = 100, message = "country name too long")
            private String country;
            
            @Size(max = 20, message = "postal code too long")
            private String postalCode;
        }
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdatePasswordRequest {
        @NotBlank(message = "current password is required")
        private String currentPassword;
        
        @NotBlank(message = "new password is required")
        @ValidPassword(message = "password must be at least 8 characters with uppercase, lowercase, number and special character")
        private String newPassword;
        
        @NotBlank(message = "password confirmation is required")
        private String confirmPassword;
        
        @AssertTrue(message = "new password and confirmation must match")
        public boolean isPasswordMatching() {
            return newPassword != null && newPassword.equals(confirmPassword);
        }
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UploadProfilePictureRequest {
        @NotBlank(message = "file data is required")
        private String fileData;
        
        @NotBlank(message = "file name is required")
        @Size(max = 255, message = "file name too long")
        private String fileName;
        
        @NotBlank(message = "file type is required")
        @Pattern(regexp = "^(image/jpeg|image/jpg|image/png)$", message = "only JPEG and PNG images are allowed")
        private String fileType;
        
        @Max(value = ********, message = "file size cannot exceed 10MB")
        private Long fileSize;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeleteAccountRequest {
        @NotBlank(message = "password is required")
        private String password;
        
        @NotBlank(message = "confirmation is required")
        @Pattern(regexp = "^DELETE$", message = "type DELETE to confirm account deletion")
        private String confirmation;
        
        @Size(max = 500, message = "reason too long")
        private String reason;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransferRequest {
        @NotNull(message = "amount is required")
        @DecimalMin(value = "0.01", message = "amount must be greater than 0")
        @DecimalMax(value = "1000000.00", message = "amount exceeds maximum limit")
        @Digits(integer = 10, fraction = 2, message = "amount format invalid")
        private BigDecimal amount;
        
        @NotBlank(message = "currency is required")
        @ValidCurrency(message = "invalid currency")
        private String currency;
        
        @NotBlank(message = "recipient phone is required")
        @ValidPhone(message = "recipient phone must be in international format")
        private String recipientPhone;
        
        @Size(max = 200, message = "description too long")
        private String description;
        
        @NotBlank(message = "transaction PIN is required")
        @ValidPin(message = "PIN must be 4 digits")
        private String pin;
        
        @ValidPlatform(message = "invalid platform")
        private String platform;
    }
}
