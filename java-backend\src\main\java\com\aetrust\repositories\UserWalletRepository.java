package com.aetrust.repositories;

import com.aetrust.models.User.UserWallet;
import com.aetrust.types.Types.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserWalletRepository extends JpaRepository<UserWallet, Long> {
    
    // Find by user references
    List<UserWallet> findByUserIdAndDeletedAtIsNull(Long userId);
    List<UserWallet> findByUserUuidAndDeletedAtIsNull(UUID userUuid);
    
    // Find default wallet
    Optional<UserWallet> findByUserIdAndIsDefaultTrueAndDeletedAtIsNull(Long userId);
    Optional<UserWallet> findByUserUuidAndIsDefaultTrueAndDeletedAtIsNull(UUID userUuid);
    
    // Find by wallet type and currency
    Optional<UserWallet> findByUserIdAndWalletTypeAndCurrencyAndDeletedAtIsNull(
        Long userId, WalletType walletType, String currency);
    Optional<UserWallet> findByUserUuidAndWalletTypeAndCurrencyAndDeletedAtIsNull(
        UUID userUuid, WalletType walletType, String currency);
    
    // Find by status
    List<UserWallet> findByUserIdAndStatusAndDeletedAtIsNull(Long userId, WalletStatus status);
    List<UserWallet> findByStatusAndDeletedAtIsNull(WalletStatus status);
    
    // Find by wallet address
    Optional<UserWallet> findByWalletAddressAndDeletedAtIsNull(String walletAddress);
    
    // Balance queries
    @Query("SELECT w FROM UserWallet w WHERE w.userId = :userId AND w.balance >= :minBalance AND w.deletedAt IS NULL")
    List<UserWallet> findByUserIdAndBalanceGreaterThanEqual(@Param("userId") Long userId, @Param("minBalance") BigDecimal minBalance);
    
    @Query("SELECT w FROM UserWallet w WHERE w.balance >= :minBalance AND w.deletedAt IS NULL")
    List<UserWallet> findByBalanceGreaterThanEqual(@Param("minBalance") BigDecimal minBalance);
    
    @Query("SELECT SUM(w.balance) FROM UserWallet w WHERE w.userId = :userId AND w.status = :status AND w.deletedAt IS NULL")
    BigDecimal getTotalBalanceByUserIdAndStatus(@Param("userId") Long userId, @Param("status") WalletStatus status);
    
    @Query("SELECT SUM(w.availableBalance) FROM UserWallet w WHERE w.userId = :userId AND w.status = :status AND w.deletedAt IS NULL")
    BigDecimal getTotalAvailableBalanceByUserIdAndStatus(@Param("userId") Long userId, @Param("status") WalletStatus status);
    
    // Transaction statistics
    @Query("SELECT w FROM UserWallet w WHERE w.totalTransactions >= :minTransactions AND w.deletedAt IS NULL")
    List<UserWallet> findByTotalTransactionsGreaterThanEqual(@Param("minTransactions") Integer minTransactions);
    
    @Query("SELECT w FROM UserWallet w WHERE w.lastTransactionDate >= :date AND w.deletedAt IS NULL")
    List<UserWallet> findByLastTransactionDateAfter(@Param("date") LocalDateTime date);
    
    // Currency statistics
    @Query("SELECT w.currency, COUNT(w) FROM UserWallet w WHERE w.deletedAt IS NULL GROUP BY w.currency")
    List<Object[]> getWalletCountByCurrency();
    
    @Query("SELECT w.currency, SUM(w.balance) FROM UserWallet w WHERE w.status = :status AND w.deletedAt IS NULL GROUP BY w.currency")
    List<Object[]> getTotalBalanceByCurrency(@Param("status") WalletStatus status);
    
    // Existence checks
    boolean existsByUserIdAndWalletTypeAndDeletedAtIsNull(Long userId, WalletType walletType);
    boolean existsByUserUuidAndWalletTypeAndDeletedAtIsNull(UUID userUuid, WalletType walletType);
    boolean existsByWalletAddressAndDeletedAtIsNull(String walletAddress);
    
    // Count queries
    @Query("SELECT COUNT(w) FROM UserWallet w WHERE w.userId = :userId AND w.status = :status AND w.deletedAt IS NULL")
    long countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") WalletStatus status);
    
    // Soft delete
    @Modifying
    @Query("UPDATE UserWallet w SET w.deletedAt = :deletedAt WHERE w.userId = :userId")
    void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);
    
    @Modifying
    @Query("UPDATE UserWallet w SET w.deletedAt = :deletedAt WHERE w.userUuid = :userUuid")
    void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);
    
    // Update balance operations
    @Modifying
    @Query("UPDATE UserWallet w SET w.balance = w.balance + :amount, w.availableBalance = w.availableBalance + :amount WHERE w.id = :walletId")
    void creditWallet(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);
    
    @Modifying
    @Query("UPDATE UserWallet w SET w.balance = w.balance - :amount, w.availableBalance = w.availableBalance - :amount WHERE w.id = :walletId")
    void debitWallet(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);
    
    @Modifying
    @Query("UPDATE UserWallet w SET w.availableBalance = w.availableBalance - :amount, w.pendingBalance = w.pendingBalance + :amount WHERE w.id = :walletId")
    void freezeAmount(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);
    
    @Modifying
    @Query("UPDATE UserWallet w SET w.availableBalance = w.availableBalance + :amount, w.pendingBalance = w.pendingBalance - :amount WHERE w.id = :walletId")
    void unfreezeAmount(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);
}
